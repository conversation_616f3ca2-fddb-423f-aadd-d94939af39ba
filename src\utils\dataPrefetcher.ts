import { supabaseAPI } from '../services/supabase';

/**
 * Data Prefetcher Utility
 * Preloads commonly accessed data to improve user experience
 */

interface PrefetchOptions {
  priority?: 'high' | 'medium' | 'low';
  delay?: number; // Delay in milliseconds before prefetching
}

class DataPrefetcher {
  private prefetchQueue: Array<{ fn: () => Promise<any>; priority: string; delay: number }> = [];
  private isProcessing = false;

  /**
   * Add a prefetch task to the queue
   */
  addTask(fetchFunction: () => Promise<any>, options: PrefetchOptions = {}) {
    const { priority = 'medium', delay = 0 } = options;
    
    this.prefetchQueue.push({
      fn: fetchFunction,
      priority,
      delay
    });

    // Sort by priority (high -> medium -> low)
    this.prefetchQueue.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority as keyof typeof priorityOrder] - 
             priorityOrder[a.priority as keyof typeof priorityOrder];
    });

    this.processQueue();
  }

  /**
   * Process the prefetch queue
   */
  private async processQueue() {
    if (this.isProcessing || this.prefetchQueue.length === 0) return;

    this.isProcessing = true;

    while (this.prefetchQueue.length > 0) {
      const task = this.prefetchQueue.shift();
      if (!task) break;

      try {
        // Wait for delay if specified
        if (task.delay > 0) {
          await new Promise(resolve => setTimeout(resolve, task.delay));
        }

        // Execute the prefetch task
        console.log(`🚀 Prefetching data with ${task.priority} priority`);
        await task.fn();
      } catch (error) {
        console.warn('Prefetch task failed:', error);
      }

      // Small delay between tasks to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.isProcessing = false;
  }

  /**
   * Clear all pending prefetch tasks
   */
  clearQueue() {
    this.prefetchQueue = [];
    console.log('🗑️ Prefetch queue cleared');
  }

  /**
   * Get queue status
   */
  getQueueStatus() {
    return {
      pending: this.prefetchQueue.length,
      processing: this.isProcessing,
      tasks: this.prefetchQueue.map(task => ({
        priority: task.priority,
        delay: task.delay
      }))
    };
  }
}

// Singleton instance
export const dataPrefetcher = new DataPrefetcher();

/**
 * Prefetch strategies for different scenarios
 */
export const prefetchStrategies = {
  /**
   * Dashboard prefetch - load all dashboard-related data
   */
  dashboard: () => {
    console.log('📊 Starting dashboard prefetch strategy');
    
    // High priority: Dashboard stats (needed immediately)
    dataPrefetcher.addTask(
      () => supabaseAPI.dashboard.getStats(),
      { priority: 'high' }
    );

    // Medium priority: Core data (likely to be accessed soon)
    dataPrefetcher.addTask(
      () => supabaseAPI.vehicles.getAll(),
      { priority: 'medium', delay: 500 }
    );

    dataPrefetcher.addTask(
      () => supabaseAPI.drivers.getAll(),
      { priority: 'medium', delay: 1000 }
    );

    // Low priority: Secondary data (might be accessed)
    dataPrefetcher.addTask(
      () => supabaseAPI.maintenance.getAll(),
      { priority: 'low', delay: 2000 }
    );

    dataPrefetcher.addTask(
      () => supabaseAPI.fuel.getAll(),
      { priority: 'low', delay: 3000 }
    );
  },

  /**
   * Reports prefetch - load data needed for reports
   */
  reports: () => {
    console.log('📈 Starting reports prefetch strategy');
    
    dataPrefetcher.addTask(
      () => supabaseAPI.vehicles.getAll(),
      { priority: 'high' }
    );

    dataPrefetcher.addTask(
      () => supabaseAPI.drivers.getAll(),
      { priority: 'high', delay: 200 }
    );

    dataPrefetcher.addTask(
      () => supabaseAPI.fuel.getAll(),
      { priority: 'medium', delay: 500 }
    );

    dataPrefetcher.addTask(
      () => supabaseAPI.maintenance.getAll(),
      { priority: 'medium', delay: 1000 }
    );
  },

  /**
   * Management prefetch - load data for management pages
   */
  management: () => {
    console.log('⚙️ Starting management prefetch strategy');
    
    dataPrefetcher.addTask(
      () => supabaseAPI.branches.getAll(),
      { priority: 'high' }
    );

    dataPrefetcher.addTask(
      () => supabaseAPI.users.getAll(),
      { priority: 'medium', delay: 500 }
    );

    dataPrefetcher.addTask(
      () => supabaseAPI.thresholds.getAll(),
      { priority: 'medium', delay: 1000 }
    );
  },

  /**
   * Idle prefetch - load data when user is idle
   */
  idle: () => {
    console.log('😴 Starting idle prefetch strategy');
    
    // Prefetch all data with low priority and longer delays
    const dataTypes = [
      () => supabaseAPI.vehicles.getAll(),
      () => supabaseAPI.drivers.getAll(),
      () => supabaseAPI.branches.getAll(),
      () => supabaseAPI.maintenance.getAll(),
      () => supabaseAPI.fuel.getAll(),
      () => supabaseAPI.users.getAll(),
      () => supabaseAPI.thresholds.getAll(),
      () => supabaseAPI.notifications.getAll()
    ];

    dataTypes.forEach((fetchFn, index) => {
      dataPrefetcher.addTask(fetchFn, {
        priority: 'low',
        delay: index * 2000 // 2 seconds between each
      });
    });
  }
};

/**
 * Auto-prefetch based on current route
 */
export const autoPrefetch = (currentPath: string) => {
  switch (true) {
    case currentPath === '/' || currentPath === '/dashboard':
      prefetchStrategies.dashboard();
      break;
    case currentPath === '/reports':
      prefetchStrategies.reports();
      break;
    case currentPath.includes('/vehicles') || 
         currentPath.includes('/drivers') || 
         currentPath.includes('/branches'):
      prefetchStrategies.management();
      break;
    default:
      // For unknown routes, use idle strategy
      setTimeout(() => prefetchStrategies.idle(), 5000);
  }
};

/**
 * Setup idle detection for prefetching
 */
export const setupIdlePrefetch = () => {
  let idleTimer: NodeJS.Timeout;
  const IDLE_TIME = 10000; // 10 seconds

  const resetIdleTimer = () => {
    clearTimeout(idleTimer);
    idleTimer = setTimeout(() => {
      prefetchStrategies.idle();
    }, IDLE_TIME);
  };

  // Listen for user activity
  const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
  events.forEach(event => {
    document.addEventListener(event, resetIdleTimer, true);
  });

  // Start the timer
  resetIdleTimer();

  // Return cleanup function
  return () => {
    clearTimeout(idleTimer);
    events.forEach(event => {
      document.removeEventListener(event, resetIdleTimer, true);
    });
  };
};
