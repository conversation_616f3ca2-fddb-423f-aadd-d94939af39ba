/**
 * Comprehensive Test Script for Fleet Management System
 * Tests both demo mode and live database mode functionality
 */

const puppeteer = require('puppeteer');

const TEST_URL = 'http://localhost:5174';
const DEMO_CREDENTIALS = { email: '<EMAIL>', password: 'demo123' };

class FMSTestSuite {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      demoMode: { passed: 0, failed: 0, tests: [] },
      liveMode: { passed: 0, failed: 0, tests: [] }
    };
  }

  async setup() {
    console.log('🚀 Setting up test environment...');
    this.browser = await puppeteer.launch({ 
      headless: false, 
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Console Error:', msg.text());
      }
    });
  }

  async teardown() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runTest(testName, testFn, mode = 'demo') {
    try {
      console.log(`🧪 Running ${mode} test: ${testName}`);
      await testFn();
      this.results[mode + 'Mode'].passed++;
      this.results[mode + 'Mode'].tests.push({ name: testName, status: 'PASSED' });
      console.log(`✅ ${testName} - PASSED`);
    } catch (error) {
      this.results[mode + 'Mode'].failed++;
      this.results[mode + 'Mode'].tests.push({ name: testName, status: 'FAILED', error: error.message });
      console.log(`❌ ${testName} - FAILED: ${error.message}`);
    }
  }

  async waitForElement(selector, timeout = 10000) {
    await this.page.waitForSelector(selector, { timeout });
  }

  async waitForText(text, timeout = 10000) {
    await this.page.waitForFunction(
      (text) => document.body.innerText.includes(text),
      { timeout },
      text
    );
  }

  // Test login functionality
  async testLogin() {
    await this.page.goto(TEST_URL);
    await this.waitForElement('input[type="email"]');
    
    await this.page.type('input[type="email"]', DEMO_CREDENTIALS.email);
    await this.page.type('input[type="password"]', DEMO_CREDENTIALS.password);
    await this.page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await this.waitForText('Dashboard', 15000);
  }

  // Test dashboard loading and statistics
  async testDashboard() {
    await this.waitForElement('[data-testid="dashboard"], .dashboard, h1');
    
    // Check for statistical cards
    const statsCards = await this.page.$$('.bg-white.rounded-lg.shadow, .stat-card, [class*="card"]');
    if (statsCards.length === 0) {
      throw new Error('No dashboard statistics cards found');
    }
    
    // Check for charts or data visualization
    const hasCharts = await this.page.$('.recharts-wrapper, canvas, svg') !== null;
    if (!hasCharts) {
      console.log('⚠️ No charts detected on dashboard');
    }
  }

  // Test navigation between pages
  async testNavigation() {
    const pages = [
      { name: 'Vehicles', selector: 'a[href*="vehicles"], a:contains("Vehicles")' },
      { name: 'Drivers', selector: 'a[href*="drivers"], a:contains("Drivers")' },
      { name: 'Reports', selector: 'a[href*="reports"], a:contains("Reports")' }
    ];

    for (const pageInfo of pages) {
      try {
        // Try to find and click navigation link
        await this.page.evaluate((selector) => {
          const link = document.querySelector(selector) || 
                      Array.from(document.querySelectorAll('a')).find(a => 
                        a.textContent.toLowerCase().includes(selector.toLowerCase())
                      );
          if (link) link.click();
        }, pageInfo.name.toLowerCase());
        
        await this.page.waitForTimeout(2000); // Wait for navigation
        
        // Check if page loaded
        const currentUrl = this.page.url();
        if (!currentUrl.includes(pageInfo.name.toLowerCase())) {
          console.log(`⚠️ Navigation to ${pageInfo.name} may not have worked (URL: ${currentUrl})`);
        }
      } catch (error) {
        console.log(`⚠️ Could not navigate to ${pageInfo.name}: ${error.message}`);
      }
    }
  }

  // Test data loading and display
  async testDataLoading() {
    // Go back to dashboard
    await this.page.goto(TEST_URL + '/dashboard');
    await this.page.waitForTimeout(3000);
    
    // Check for loading states
    const hasLoadingStates = await this.page.evaluate(() => {
      return document.body.innerText.includes('Loading') || 
             document.querySelector('.animate-spin, .loading, .skeleton') !== null;
    });
    
    // Wait for data to load
    await this.page.waitForTimeout(5000);
    
    // Check for error states
    const hasErrors = await this.page.evaluate(() => {
      return document.body.innerText.includes('Error') || 
             document.body.innerText.includes('Failed to load');
    });
    
    if (hasErrors) {
      throw new Error('Data loading errors detected');
    }
  }

  // Test notifications system
  async testNotifications() {
    // Look for notification bell
    const notificationBell = await this.page.$('button:has(svg), [class*="bell"], [data-testid="notifications"]');
    
    if (notificationBell) {
      await notificationBell.click();
      await this.page.waitForTimeout(1000);
      
      // Check if dropdown appeared
      const dropdown = await this.page.$('[class*="dropdown"], [class*="notification"]');
      if (!dropdown) {
        console.log('⚠️ Notification dropdown did not appear');
      }
    } else {
      console.log('⚠️ Notification bell not found');
    }
  }

  // Test performance monitor (development only)
  async testPerformanceMonitor() {
    const perfButton = await this.page.$('button:has(svg[class*="activity"]), [title*="Performance"]');
    
    if (perfButton) {
      await perfButton.click();
      await this.page.waitForTimeout(1000);
      
      // Check if performance panel appeared
      const perfPanel = await this.page.$('[class*="performance"], [class*="monitor"]');
      if (!perfPanel) {
        console.log('⚠️ Performance monitor panel did not appear');
      }
    } else {
      console.log('ℹ️ Performance monitor not found (expected in production)');
    }
  }

  // Test responsive design
  async testResponsive() {
    // Test mobile viewport
    await this.page.setViewport({ width: 375, height: 667 });
    await this.page.waitForTimeout(1000);
    
    // Check if mobile menu exists
    const mobileMenu = await this.page.$('button:has(svg), [class*="menu"], [data-testid="mobile-menu"]');
    if (!mobileMenu) {
      console.log('⚠️ Mobile menu button not found');
    }
    
    // Reset to desktop
    await this.page.setViewport({ width: 1280, height: 720 });
  }

  // Run all demo mode tests
  async runDemoTests() {
    console.log('\n📱 Starting Demo Mode Tests...');
    
    await this.runTest('Login', () => this.testLogin(), 'demo');
    await this.runTest('Dashboard Loading', () => this.testDashboard(), 'demo');
    await this.runTest('Navigation', () => this.testNavigation(), 'demo');
    await this.runTest('Data Loading', () => this.testDataLoading(), 'demo');
    await this.runTest('Notifications', () => this.testNotifications(), 'demo');
    await this.runTest('Performance Monitor', () => this.testPerformanceMonitor(), 'demo');
    await this.runTest('Responsive Design', () => this.testResponsive(), 'demo');
  }

  // Run all live mode tests (if database is available)
  async runLiveTests() {
    console.log('\n🔗 Starting Live Mode Tests...');
    
    // For live mode, we would use real credentials
    // This is a placeholder - in real scenario, you'd have test credentials
    console.log('ℹ️ Live mode tests would require real database credentials');
    console.log('ℹ️ Skipping live mode tests for security reasons');
  }

  // Generate test report
  generateReport() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const demoResults = this.results.demoMode;
    const liveResults = this.results.liveMode;
    
    console.log(`\n📱 Demo Mode: ${demoResults.passed} passed, ${demoResults.failed} failed`);
    demoResults.tests.forEach(test => {
      const icon = test.status === 'PASSED' ? '✅' : '❌';
      console.log(`  ${icon} ${test.name}`);
      if (test.error) console.log(`     Error: ${test.error}`);
    });
    
    console.log(`\n🔗 Live Mode: ${liveResults.passed} passed, ${liveResults.failed} failed`);
    liveResults.tests.forEach(test => {
      const icon = test.status === 'PASSED' ? '✅' : '❌';
      console.log(`  ${icon} ${test.name}`);
      if (test.error) console.log(`     Error: ${test.error}`);
    });
    
    const totalPassed = demoResults.passed + liveResults.passed;
    const totalFailed = demoResults.failed + liveResults.failed;
    const totalTests = totalPassed + totalFailed;
    
    console.log(`\n🎯 Overall: ${totalPassed}/${totalTests} tests passed (${Math.round(totalPassed/totalTests*100)}%)`);
    
    if (totalFailed === 0) {
      console.log('🎉 All tests passed!');
    } else {
      console.log(`⚠️ ${totalFailed} test(s) failed`);
    }
  }

  // Main test runner
  async run() {
    try {
      await this.setup();
      await this.runDemoTests();
      await this.runLiveTests();
      this.generateReport();
    } catch (error) {
      console.error('💥 Test suite failed:', error);
    } finally {
      await this.teardown();
    }
  }
}

// Run the test suite
if (require.main === module) {
  const testSuite = new FMSTestSuite();
  testSuite.run().catch(console.error);
}

module.exports = FMSTestSuite;
