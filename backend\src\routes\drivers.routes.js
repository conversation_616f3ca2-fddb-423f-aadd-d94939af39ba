const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth.middleware');
const {
  getAllDrivers,
  getDriverById,
  createDriver,
  updateDriver,
  deleteDriver
} = require('../controllers/drivers.controller');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all drivers
router.get('/', getAllDrivers);

// Get driver by ID
router.get('/:id', getDriverById);

// Create new driver (Manager and above)
router.post('/', requireRole(['Super Admin', 'Admin', 'Manager']), createDriver);

// Update driver (Manager and above)
router.put('/:id', requireRole(['Super Admin', 'Admin', 'Manager']), updateDriver);

// Delete driver (Admin and above)
router.delete('/:id', requireRole(['Super Admin', 'Admin']), deleteDriver);

module.exports = router;