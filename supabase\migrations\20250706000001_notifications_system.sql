-- Create notifications table for the Fleet Management System
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(50) NOT NULL DEFAULT 'info', -- 'info', 'warning', 'error', 'success'
  severity VARCHAR(20) NOT NULL DEFAULT 'low', -- 'low', 'medium', 'high', 'urgent'
  read BOOLEAN DEFAULT FALSE,
  action_url VARCHAR(500), -- Optional URL for action button
  action_text VARCHAR(100), -- Optional text for action button
  metadata JSONB, -- Additional data for the notification
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE -- Optional expiration date
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_severity ON notifications(severity);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert notifications for any user" ON notifications
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can delete expired notifications" ON notifications
    FOR DELETE USING (expires_at IS NOT NULL AND expires_at < NOW());

-- Create function to automatically create notifications for system events
CREATE OR REPLACE FUNCTION create_system_notification(
    p_user_id UUID,
    p_title VARCHAR(255),
    p_message TEXT,
    p_type VARCHAR(50) DEFAULT 'info',
    p_severity VARCHAR(20) DEFAULT 'low',
    p_action_url VARCHAR(500) DEFAULT NULL,
    p_action_text VARCHAR(100) DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL,
    p_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO notifications (
        user_id, title, message, type, severity, 
        action_url, action_text, metadata, expires_at
    ) VALUES (
        p_user_id, p_title, p_message, p_type, p_severity,
        p_action_url, p_action_text, p_metadata, p_expires_at
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to broadcast notifications to all users with specific roles
CREATE OR REPLACE FUNCTION create_broadcast_notification(
    p_title VARCHAR(255),
    p_message TEXT,
    p_type VARCHAR(50) DEFAULT 'info',
    p_severity VARCHAR(20) DEFAULT 'low',
    p_target_roles TEXT[] DEFAULT ARRAY['Admin', 'Manager', 'Supervisor', 'Driver'],
    p_action_url VARCHAR(500) DEFAULT NULL,
    p_action_text VARCHAR(100) DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL,
    p_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    user_record RECORD;
    notification_count INTEGER := 0;
BEGIN
    -- Insert notification for each user with matching role
    FOR user_record IN 
        SELECT id FROM users 
        WHERE role = ANY(p_target_roles) AND status = 'Active'
    LOOP
        PERFORM create_system_notification(
            user_record.id, p_title, p_message, p_type, p_severity,
            p_action_url, p_action_text, p_metadata, p_expires_at
        );
        notification_count := notification_count + 1;
    END LOOP;
    
    RETURN notification_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean up expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM notifications 
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert some sample notifications for testing
INSERT INTO notifications (user_id, title, message, type, severity, metadata) 
SELECT 
    u.id,
    'Welcome to Fleet Management System',
    'Your account has been successfully created. Start managing your fleet efficiently!',
    'success',
    'low',
    '{"welcome": true, "version": "1.0"}'::jsonb
FROM users u 
WHERE u.role = 'Admin'
LIMIT 1;

-- Create maintenance due notifications
INSERT INTO notifications (user_id, title, message, type, severity, action_url, action_text, metadata)
SELECT 
    u.id,
    'Vehicle Maintenance Due',
    'Vehicle ' || v.license_plate || ' requires scheduled maintenance within 7 days.',
    'warning',
    'medium',
    '/maintenance',
    'Schedule Maintenance',
    jsonb_build_object('vehicle_id', v.id, 'license_plate', v.license_plate)
FROM vehicles v
CROSS JOIN users u
WHERE v.vehicle_status = 'Active' 
  AND u.role IN ('Admin', 'Manager', 'Supervisor')
  AND EXISTS (
    SELECT 1 FROM maintenance m 
    WHERE m.vehicle_id = v.id 
      AND m.next_service_date <= (CURRENT_DATE + INTERVAL '7 days')
      AND m.status != 'Completed'
  )
LIMIT 5;
