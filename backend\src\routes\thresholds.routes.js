const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth.middleware');
const {
  getAllThresholds,
  getThresholdById,
  createThreshold,
  updateThreshold,
  deleteThreshold
} = require('../controllers/thresholds.controller');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all thresholds
router.get('/', getAllThresholds);

// Get threshold by ID
router.get('/:id', getThresholdById);

// Create new threshold (Admin and above)
router.post('/', requireRole(['Super Admin', 'Admin']), createThreshold);

// Update threshold (Admin and above)
router.put('/:id', requireRole(['Super Admin', 'Admin']), updateThreshold);

// Delete threshold (Super Admin only)
router.delete('/:id', requireRole(['Super Admin']), deleteThreshold);

module.exports = router;