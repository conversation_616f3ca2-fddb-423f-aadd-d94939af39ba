const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth.middleware');
const {
  getAllVehicles,
  getVehicleById,
  createVehicle,
  updateVehicle,
  deleteVehicle,
  getVehicleStats
} = require('../controllers/vehicles.controller');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all vehicles
router.get('/', getAllVehicles);

// Get vehicle statistics
router.get('/stats', getVehicleStats);

// Get vehicle by ID
router.get('/:id', getVehicleById);

// Create new vehicle (Manager and above)
router.post('/', requireRole(['Super Admin', 'Admin', 'Manager']), createVehicle);

// Update vehicle (Manager and above)
router.put('/:id', requireRole(['Super Admin', 'Admin', 'Manager']), updateVehicle);

// Delete vehicle (Admin and above)
router.delete('/:id', requireRole(['Super Admin', 'Admin']), deleteVehicle);

module.exports = router;