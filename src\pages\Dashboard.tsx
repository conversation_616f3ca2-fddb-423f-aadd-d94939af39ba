import React, { useState, useEffect } from 'react';
import { Car, Users, MapPin, Wrench, Fuel, AlertTriangle, TrendingUp, Activity } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { dashboardAPI } from '../services/api';
import { supabaseAPI } from '../services/supabase';

interface DashboardStats {
  totalVehicles: number;
  activeVehicles: number;
  totalDrivers: number;
  activeDrivers: number;
  totalBranches: number;
  pendingMaintenance: number;
  fuelConsumption: number;
  alerts: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalVehicles: 0,
    activeVehicles: 0,
    totalDrivers: 0,
    activeDrivers: 0,
    totalBranches: 0,
    pendingMaintenance: 0,
    fuelConsumption: 0,
    alerts: 0
  });

  const [loading, setLoading] = useState(true);

  const [fuelData, setFuelData] = useState<Array<{ month: string; consumption: number }>>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch dashboard stats
        const data = await dashboardAPI.getStats();
        setStats(data);

        // Fetch fuel records for the chart
        const { data: fuelRecords } = await supabaseAPI.fuel.getAll();
        
        // Process fuel data by month
        const monthlyConsumption = new Map<string, number>();
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        fuelRecords?.forEach(record => {
          const date = new Date(record.datetime);
          const monthKey = months[date.getMonth()];
          const currentAmount = monthlyConsumption.get(monthKey) || 0;
          monthlyConsumption.set(monthKey, currentAmount + (record.quantity || 0));
        });

        // Convert to chart data format
        const chartData = Array.from(monthlyConsumption.entries())
          .map(([month, consumption]) => ({
            month,
            consumption: Math.round(consumption)
          }))
          .sort((a, b) => months.indexOf(a.month) - months.indexOf(b.month));

        setFuelData(chartData);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const vehicleStatusData = [
    {
      name: 'Active',
      value: stats.activeVehicles,
      color: '#10B981'
    },
    {
      name: 'Maintenance',
      value: stats.pendingMaintenance,
      color: '#F59E0B'
    },
    {
      name: 'Inactive',
      value: Math.max(0, stats.totalVehicles - stats.activeVehicles - stats.pendingMaintenance),
      color: '#EF4444'
    }
  ].filter(item => item.value > 0);

  const statCards = [
    {
      title: 'Total Vehicles',
      value: stats.totalVehicles,
      icon: Car,
      color: 'bg-blue-500',
      trend: '+2.5%'
    },
    {
      title: 'Active Drivers',
      value: stats.activeDrivers,
      icon: Users,
      color: 'bg-green-500',
      trend: '+1.2%'
    },
    {
      title: 'Branches',
      value: stats.totalBranches,
      icon: MapPin,
      color: 'bg-purple-500',
      trend: '0%'
    },
    {
      title: 'Pending Maintenance',
      value: stats.pendingMaintenance,
      icon: Wrench,
      color: 'bg-orange-500',
      trend: '-5.3%'
    },
    {
      title: 'Fuel Consumption',
      value: `${stats.fuelConsumption}L`,
      icon: Fuel,
      color: 'bg-indigo-500',
      trend: '+3.1%'
    },
    {
      title: 'Alerts',
      value: stats.alerts,
      icon: AlertTriangle,
      color: 'bg-red-500',
      trend: '-12.5%'
    }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Fleet Dashboard</h1>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Activity className="h-4 w-4" />
          <span>Last updated: {new Date().toLocaleTimeString()}</span>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div key={index} className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">{card.trend}</span>
                  </div>
                </div>
                <div className={`${card.color} p-3 rounded-full`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Fuel Consumption Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Fuel Consumption Trend</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={fuelData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="consumption" 
                stroke="#3B82F6" 
                strokeWidth={2}
                dot={{ fill: '#3B82F6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Vehicle Status Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Vehicle Status Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={vehicleStatusData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {vehicleStatusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
          <div className="flex justify-center space-x-6 mt-4">
            {vehicleStatusData.map((entry, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: entry.color }}
                ></div>
                <span className="text-sm text-gray-600">{entry.name}: {entry.value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[
              { action: 'Vehicle VH001 completed maintenance', time: '2 hours ago', type: 'maintenance' },
              { action: 'New driver John Smith added to fleet', time: '4 hours ago', type: 'driver' },
              { action: 'Fuel refill completed for VH015', time: '6 hours ago', type: 'fuel' },
              { action: 'Vehicle VH008 scheduled for maintenance', time: '1 day ago', type: 'maintenance' },
              { action: 'Branch office opened in New York', time: '2 days ago', type: 'branch' },
            ].map((activity, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg">
                <div className={`w-2 h-2 rounded-full ${
                  activity.type === 'maintenance' ? 'bg-orange-500' :
                  activity.type === 'driver' ? 'bg-green-500' :
                  activity.type === 'fuel' ? 'bg-blue-500' : 'bg-purple-500'
                }`}></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.action}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;