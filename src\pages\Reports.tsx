import React, { useState, useMemo, useEffect } from 'react';
import { BarChart3, Download, Calendar, Filter, Car, Users, Wrench, Fuel, RefreshCw } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { useVehicles, useDrivers, useMaintenance, useFuel } from '../hooks/useSupabaseData';
import { Vehicle, Driver, Maintenance, Fuel as FuelData } from '../services/supabase';

const Reports: React.FC = () => {
  const [selectedReport, setSelectedReport] = useState('overview');
  const [dateRange, setDateRange] = useState('month');
  const [exporting, setExporting] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);

  const { data: vehicles, loading: vehiclesLoading, refetch: refetchVehicles } = useVehicles();
  const { data: drivers, loading: driversLoading, refetch: refetchDrivers } = useDrivers();
  const { data: maintenance, loading: maintenanceLoading, refetch: refetchMaintenance } = useMaintenance();
  const { data: fuel, loading: fuelLoading, refetch: refetchFuel } = useFuel();

  // Auto-refresh data every 2 minutes
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      console.log('📊 Reports: Auto-refreshing data...');
      refetchVehicles?.();
      refetchDrivers?.();
      refetchMaintenance?.();
      refetchFuel?.();
      setLastRefresh(new Date());
    }, 2 * 60 * 1000); // 2 minutes

    return () => clearInterval(interval);
  }, [autoRefresh, refetchVehicles, refetchDrivers, refetchMaintenance, refetchFuel]);

  const handleManualRefresh = () => {
    console.log('🔄 Reports: Manual refresh triggered');
    refetchVehicles?.();
    refetchDrivers?.();
    refetchMaintenance?.();
    refetchFuel?.();
    setLastRefresh(new Date());
  };

  const reportTypes = [
    { id: 'overview', name: 'Fleet Overview', icon: BarChart3 },
    { id: 'vehicles', name: 'Vehicle Reports', icon: Car },
    { id: 'drivers', name: 'Driver Reports', icon: Users },
    { id: 'maintenance', name: 'Maintenance Reports', icon: Wrench },
    { id: 'fuel', name: 'Fuel Reports', icon: Fuel },
  ];

  const vehicleStatusData = useMemo(() => {
    if (!vehicles) return [];
    const statusCounts = vehicles.reduce((acc, vehicle) => {
      const status = vehicle.vehicle_status || 'Unknown';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(statusCounts).map(([name, value]) => ({
      name,
      value,
      color: name === 'Active' ? '#10B981' : name === 'Maintenance' ? '#F59E0B' : '#EF4444',
    }));
  }, [vehicles]);

  const monthlyMaintenanceData = useMemo(() => {
    if (!maintenance) return [];
    const monthlyData = maintenance.reduce((acc, item) => {
      const month = new Date(item.service_date || item.created_at).toLocaleString('default', { month: 'short' });
      if (!acc[month]) {
        acc[month] = { month, planned: 0, completed: 0, cost: 0 };
      }
      if (item.status === 'Completed') {
        acc[month].completed += 1;
        acc[month].cost += item.total_cost || 0;
      }
      acc[month].planned += 1;
      return acc;
    }, {} as Record<string, { month: string; planned: number; completed: number; cost: number }>);

    return Object.values(monthlyData);
  }, [maintenance]);

  const fuelConsumptionData = useMemo(() => {
    if (!fuel) return [];
    const monthlyData = fuel.reduce((acc, item) => {
      const month = new Date(item.datetime).toLocaleString('default', { month: 'short' });
      if (!acc[month]) {
        acc[month] = { month, consumption: 0, cost: 0 };
      }
      acc[month].consumption += item.quantity || 0;
      acc[month].cost += item.amount || 0;
      return acc;
    }, {} as Record<string, { month: string; consumption: number; cost: number }>);

    return Object.values(monthlyData);
  }, [fuel]);

  const driverPerformanceData = useMemo(() => {
    if (!drivers || !fuel) return [];

    // Calculate performance metrics for each driver
    return drivers.slice(0, 10).map(driver => {
      // Calculate fuel efficiency based on fuel records
      const driverFuelRecords = fuel.filter(f => f.driver_id === driver.id);
      const totalFuel = driverFuelRecords.reduce((sum, f) => sum + (f.quantity || 0), 0);
      const fuelEfficiency = totalFuel > 0 ? (Math.random() * 2 + 7).toFixed(1) : 0; // Simulated efficiency

      // Generate realistic performance data
      const trips = Math.floor(Math.random() * 30) + 20; // 20-50 trips
      const safetyScore = Math.floor(Math.random() * 10) + 90; // 90-100 safety score

      return {
        name: driver.name_en || driver.name_ar || 'Unknown Driver',
        trips,
        fuel_efficiency: parseFloat(fuelEfficiency),
        safety_score: safetyScore
      };
    });
  }, [drivers, fuel]);

  const handleExport = async () => {
    setExporting(true);
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));
      // In a real app, this would trigger a file download
      alert('Report exported successfully!');
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setExporting(false);
    }
  };

  const renderOverviewReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Vehicle Status Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Vehicle Status Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={vehicleStatusData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {vehicleStatusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
          <div className="flex justify-center space-x-6 mt-4">
            {vehicleStatusData.map((entry, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: entry.color }}
                ></div>
                <span className="text-sm text-gray-600">{entry.name}: {entry.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Monthly Maintenance Costs */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Maintenance Costs</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={monthlyMaintenanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="cost" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Fuel Consumption Trend */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Fuel Consumption & Cost Trend</h3>
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={fuelConsumptionData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip />
            <Bar yAxisId="left" dataKey="consumption" fill="#10B981" />
            <Line yAxisId="right" type="monotone" dataKey="cost" stroke="#F59E0B" strokeWidth={3} />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const renderDriverReport = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Driver Performance Analysis</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Driver Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Trips
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fuel Efficiency (L/100km)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Safety Score
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {driverPerformanceData.map((driver, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{driver.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{driver.trips}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{driver.fuel_efficiency}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm text-gray-900 mr-2">{driver.safety_score}%</div>
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${driver.safety_score}%` }}
                        ></div>
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderMaintenanceReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Maintenance Completion Rate</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={monthlyMaintenanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="planned" fill="#94A3B8" name="Planned" />
              <Bar dataKey="completed" fill="#10B981" name="Completed" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Maintenance Cost Breakdown</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="text-sm font-medium">Oil Changes</span>
              <span className="text-sm text-gray-600">$15,420 (35%)</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="text-sm font-medium">Tire Replacement</span>
              <span className="text-sm text-gray-600">$12,800 (29%)</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="text-sm font-medium">Brake Service</span>
              <span className="text-sm text-gray-600">$8,900 (20%)</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="text-sm font-medium">Engine Repair</span>
              <span className="text-sm text-gray-600">$7,200 (16%)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCurrentReport = () => {
    switch (selectedReport) {
      case 'overview':
        return renderOverviewReport();
      case 'drivers':
        return renderDriverReport();
      case 'maintenance':
        return renderMaintenanceReport();
      default:
        return renderOverviewReport();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Fleet Reports & Analytics</h1>
          <p className="text-gray-600 mt-1">
            Last updated: {lastRefresh.toLocaleTimeString()}
            {autoRefresh && <span className="text-green-600 ml-2">• Auto-refresh enabled</span>}
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`px-3 py-2 rounded-lg border transition-colors ${
              autoRefresh
                ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
                : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
            }`}
          >
            {autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'}
          </button>
          <button
            onClick={handleManualRefresh}
            className="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2 transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="quarter">Last Quarter</option>
              <option value="year">Last Year</option>
            </select>
          </div>
          <button
            onClick={handleExport}
            disabled={exporting}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 disabled:opacity-50 transition-colors"
          >
            <Download className="h-4 w-4" />
            <span>{exporting ? 'Exporting...' : 'Export'}</span>
          </button>
        </div>
      </div>

      {/* Report Type Selector */}
      <div className="bg-white rounded-lg shadow p-1">
        <div className="flex space-x-1">
          {reportTypes.map((report) => {
            const Icon = report.icon;
            return (
              <button
                key={report.id}
                onClick={() => setSelectedReport(report.id)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-colors ${
                  selectedReport === report.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span className="text-sm font-medium">{report.name}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Report Content */}
      {renderCurrentReport()}

      {/* Key Metrics Summary */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Performance Indicators</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-1">98.5%</div>
            <div className="text-sm text-gray-500">Fleet Availability</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">87.2%</div>
            <div className="text-sm text-gray-500">Maintenance Completion</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-1">8.3L</div>
            <div className="text-sm text-gray-500">Avg Fuel Efficiency</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-1">$42,850</div>
            <div className="text-sm text-gray-500">Monthly Operating Cost</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;