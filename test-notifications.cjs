/**
 * Test script to verify notifications system functionality
 * Tests both the API functions and real-time updates
 */

const puppeteer = require('puppeteer');

const TEST_URL = 'http://localhost:5175';
const DEMO_CREDENTIALS = { email: '<EMAIL>', password: 'demo123' };

class NotificationsTest {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async setup() {
    console.log('🚀 Setting up notifications test...');
    this.browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1280, height: 720 }
    });
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Browser Error:', msg.text());
      }
    });
  }

  async login() {
    console.log('🔐 Logging in...');
    await this.page.goto(TEST_URL);
    await this.page.waitForSelector('input[type="email"]', { timeout: 10000 });
    
    await this.page.type('input[type="email"]', DEMO_CREDENTIALS.email);
    await this.page.type('input[type="password"]', DEMO_CREDENTIALS.password);
    await this.page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await this.page.waitForSelector('[data-testid="dashboard"], .dashboard, h1', { timeout: 15000 });
    console.log('✅ Login successful');
  }

  async testNotificationDropdown() {
    console.log('🔔 Testing notification dropdown...');
    
    // Look for notification bell icon in header
    await this.page.waitForTimeout(2000);
    
    const bellFound = await this.page.evaluate(() => {
      // Look for bell icon in various possible locations
      const selectors = [
        'button[title*="notification"]',
        'button:has(svg)',
        '.notification-bell',
        '[data-testid="notification-bell"]',
        'button svg[class*="bell"]',
        'button:has([class*="bell"])'
      ];
      
      for (const selector of selectors) {
        try {
          const element = document.querySelector(selector);
          if (element) {
            console.log('Found bell with selector:', selector);
            return { found: true, selector };
          }
        } catch (e) {
          // Continue to next selector
        }
      }
      
      // Also check for any button with bell-like content
      const buttons = Array.from(document.querySelectorAll('button'));
      for (const button of buttons) {
        if (button.innerHTML.includes('bell') || 
            button.innerHTML.includes('Bell') ||
            button.innerHTML.includes('notification')) {
          console.log('Found bell button by content');
          return { found: true, element: button };
        }
      }
      
      return { found: false };
    });
    
    if (bellFound.found) {
      console.log('✅ Notification bell found');
      return true;
    } else {
      console.log('⚠️ Notification bell not found - this is expected if notifications are in header');
      return false;
    }
  }

  async testNotificationsPage() {
    console.log('📄 Testing notifications page...');
    
    // Navigate to notifications page
    await this.page.goto(`${TEST_URL}/notifications`);
    await this.page.waitForTimeout(3000);
    
    // Check if page loaded
    const pageLoaded = await this.page.evaluate(() => {
      return document.body.innerText.includes('Notifications') || 
             document.querySelector('h1, .page-title') !== null;
    });
    
    if (pageLoaded) {
      console.log('✅ Notifications page loaded successfully');
      
      // Check for key elements
      const elements = await this.page.evaluate(() => {
        return {
          hasCreateButton: document.body.innerText.includes('Create Notification'),
          hasFilters: document.body.innerText.includes('All Types') || document.body.innerText.includes('Filter'),
          hasSearchBox: document.querySelector('input[placeholder*="search"], input[type="search"]') !== null,
          hasStatistics: document.body.innerText.includes('Total') || document.body.innerText.includes('Unread'),
          hasNotificationsList: document.querySelector('.notification-item, .notification, [data-testid="notification"]') !== null
        };
      });
      
      console.log('📊 Page elements:', elements);
      return true;
    } else {
      console.log('❌ Notifications page failed to load');
      return false;
    }
  }

  async testCreateNotification() {
    console.log('➕ Testing create notification functionality...');
    
    try {
      // Look for create button
      await this.page.waitForTimeout(2000);
      
      const createButtonClicked = await this.page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const createBtn = buttons.find(btn => 
          btn.textContent.includes('Create Notification') || 
          btn.textContent.includes('Create')
        );
        
        if (createBtn) {
          createBtn.click();
          return true;
        }
        return false;
      });
      
      if (createButtonClicked) {
        await this.page.waitForTimeout(2000);
        
        // Check if modal opened
        const modalVisible = await this.page.evaluate(() => {
          const modal = document.querySelector('.modal, [role="dialog"], .create-notification-modal');
          return modal && modal.offsetParent !== null;
        });
        
        if (modalVisible) {
          console.log('✅ Create notification modal opened successfully');
          return true;
        } else {
          console.log('⚠️ Create notification modal may not be visible');
          return false;
        }
      } else {
        console.log('⚠️ Create notification button not found');
        return false;
      }
    } catch (error) {
      console.log('❌ Error testing create notification:', error.message);
      return false;
    }
  }

  async testAPIFunctions() {
    console.log('🔧 Testing API functions...');
    
    const apiStatus = await this.page.evaluate(() => {
      return new Promise((resolve) => {
        // Check if supabaseAPI is available
        const hasSupabaseAPI = typeof window.supabaseAPI !== 'undefined';
        const hasNotificationsAPI = hasSupabaseAPI && 
          typeof window.supabaseAPI.notifications !== 'undefined';
        
        resolve({
          hasSupabaseAPI,
          hasNotificationsAPI,
          hasSupabaseClient: typeof window.supabase !== 'undefined'
        });
      });
    });
    
    console.log('🔌 API status:', apiStatus);
    return apiStatus.hasSupabaseClient;
  }

  async runAllTests() {
    try {
      await this.setup();
      await this.login();
      
      const results = {
        dropdown: await this.testNotificationDropdown(),
        page: await this.testNotificationsPage(),
        create: await this.testCreateNotification(),
        api: await this.testAPIFunctions()
      };
      
      console.log('\n📋 Test Results Summary:');
      console.log('========================');
      Object.entries(results).forEach(([test, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
      });
      
      const criticalTests = ['page', 'api'];
      const criticalPassed = criticalTests.every(test => results[test]);
      
      console.log(`\n🎯 Critical Tests: ${criticalPassed ? 'PASSED' : 'FAILED'}`);
      console.log(`📊 Overall Score: ${Object.values(results).filter(Boolean).length}/${Object.keys(results).length}`);
      
      return results;
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      return null;
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Cleanup completed');
    }
  }
}

// Run the tests
async function main() {
  const test = new NotificationsTest();
  
  try {
    await test.runAllTests();
  } finally {
    await test.cleanup();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = NotificationsTest;
