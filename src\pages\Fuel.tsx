import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Fuel as FuelIcon, Car, Calendar, TrendingUp } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { fuelAPI } from '../services/api';

interface FuelRecord {
  id: number;
  vehicle_id: number;
  vehicle_plate?: string;
  fuel_type: string;
  quantity: number;
  cost_per_liter: number;
  total_cost: number;
  odometer_reading: number;
  date: string;
  station: string;
  driver_name?: string;
}

interface FuelFormData {
  vehicle_id: number;
  vehicle_plate: string;
  fuel_type: string;
  quantity: number;
  cost_per_liter: number;
  odometer_reading: number;
  date: string;
  station: string;
  driver_name: string;
}

const Fuel: React.FC = () => {
  const [records, setRecords] = useState<FuelRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [vehicleFilter, setVehicleFilter] = useState('all');
  const [dateRange, setDateRange] = useState('month');
  const [showModal, setShowModal] = useState(false);
  const [editingRecord, setEditingRecord] = useState<FuelRecord | null>(null);
  const [formData, setFormData] = useState<FuelFormData>({
    vehicle_id: 1,
    vehicle_plate: 'VH001',
    fuel_type: 'Gasoline',
    quantity: 0,
    cost_per_liter: 0,
    odometer_reading: 0,
    date: new Date().toISOString().split('T')[0],
    station: '',
    driver_name: ''
  });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchFuelRecords();
  }, []);

  const fetchFuelRecords = async () => {
    try {
      const data = await fuelAPI.getAll();
      setRecords(data);
    } catch (error) {
      console.error('Failed to fetch fuel records:', error);
      // Mock data for demo
      setRecords([
        {
          id: 1,
          vehicle_id: 1,
          vehicle_plate: 'VH001',
          fuel_type: 'Gasoline',
          quantity: 45.5,
          cost_per_liter: 1.35,
          total_cost: 61.43,
          odometer_reading: 25000,
          date: '2024-01-15',
          station: 'Shell Station Downtown',
          driver_name: 'John Smith'
        },
        {
          id: 2,
          vehicle_id: 2,
          vehicle_plate: 'VH002',
          fuel_type: 'Gasoline',
          quantity: 38.2,
          cost_per_liter: 1.38,
          total_cost: 52.72,
          odometer_reading: 32000,
          date: '2024-01-14',
          station: 'BP Station North',
          driver_name: 'Sarah Johnson'
        },
        {
          id: 3,
          vehicle_id: 1,
          vehicle_plate: 'VH001',
          fuel_type: 'Gasoline',
          quantity: 42.1,
          cost_per_liter: 1.32,
          total_cost: 55.57,
          odometer_reading: 24500,
          date: '2024-01-10',
          station: 'Exxon Station West',
          driver_name: 'John Smith'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFuelRecord = () => {
    setEditingRecord(null);
    setFormData({
      vehicle_id: 1,
      vehicle_plate: 'VH001',
      fuel_type: 'Gasoline',
      quantity: 0,
      cost_per_liter: 0,
      odometer_reading: 0,
      date: new Date().toISOString().split('T')[0],
      station: '',
      driver_name: ''
    });
    setShowModal(true);
  };

  const handleEditRecord = (record: FuelRecord) => {
    setEditingRecord(record);
    setFormData({
      vehicle_id: record.vehicle_id,
      vehicle_plate: record.vehicle_plate || '',
      fuel_type: record.fuel_type,
      quantity: record.quantity,
      cost_per_liter: record.cost_per_liter,
      odometer_reading: record.odometer_reading,
      date: record.date,
      station: record.station,
      driver_name: record.driver_name || ''
    });
    setShowModal(true);
  };

  const handleSaveRecord = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    try {
      const total_cost = formData.quantity * formData.cost_per_liter;
      
      if (editingRecord) {
        // Update existing record
        const updatedRecord = { 
          ...editingRecord, 
          ...formData,
          total_cost
        };
        setRecords(prev => prev.map(r => r.id === editingRecord.id ? updatedRecord : r));
      } else {
        // Add new record
        const newRecord: FuelRecord = {
          id: Date.now(),
          ...formData,
          total_cost
        };
        setRecords(prev => [...prev, newRecord]);
      }
      
      setShowModal(false);
      setEditingRecord(null);
    } catch (error) {
      console.error('Failed to save fuel record:', error);
    } finally {
      setSaving(false);
    }
  };

  const filteredRecords = records.filter(record => {
    const matchesSearch = record.vehicle_plate?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.station.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.driver_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesVehicle = vehicleFilter === 'all' || record.vehicle_plate === vehicleFilter;
    
    return matchesSearch && matchesVehicle;
  });

  // Calculate totals
  const totalFuel = filteredRecords.reduce((sum, record) => sum + record.quantity, 0);
  const totalCost = filteredRecords.reduce((sum, record) => sum + record.total_cost, 0);
  const avgCostPerLiter = totalFuel > 0 ? totalCost / totalFuel : 0;

  // Chart data
  const chartData = records
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .map(record => ({
      date: new Date(record.date).toLocaleDateString(),
      cost: record.total_cost,
      quantity: record.quantity
    }));

  const uniqueVehicles = [...new Set(records.map(r => r.vehicle_plate))];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Fuel Management</h1>
        <button 
          onClick={handleAddFuelRecord}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Add Fuel Record</span>
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Fuel</p>
              <p className="text-2xl font-bold text-gray-900">{totalFuel.toFixed(1)}L</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <FuelIcon className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Cost</p>
              <p className="text-2xl font-bold text-gray-900">${totalCost.toFixed(2)}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Cost/Liter</p>
              <p className="text-2xl font-bold text-gray-900">${avgCostPerLiter.toFixed(2)}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
              <FuelIcon className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Fuel Cost Trend</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Line 
              type="monotone" 
              dataKey="cost" 
              stroke="#3B82F6" 
              strokeWidth={2}
              dot={{ fill: '#3B82F6' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row space-y-4 lg:space-y-0 lg:space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search fuel records..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <select
            value={vehicleFilter}
            onChange={(e) => setVehicleFilter(e.target.value)}
            className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Vehicles</option>
            {uniqueVehicles.map(plate => (
              <option key={plate} value={plate}>{plate}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Fuel Records */}
      <div className="space-y-4">
        {filteredRecords.map((record) => (
          <div key={record.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <FuelIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{record.station}</h3>
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {record.fuel_type}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <Car className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">Vehicle: {record.vehicle_plate}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">
                          Date: {new Date(record.date).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600">Quantity: {record.quantity}L</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600">Odometer: {record.odometer_reading.toLocaleString()} km</span>
                      </div>
                      {record.driver_name && (
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-600">Driver: {record.driver_name}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">${record.total_cost.toFixed(2)}</div>
                  <div className="text-sm text-gray-500">${record.cost_per_liter.toFixed(2)}/L</div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredRecords.length === 0 && (
        <div className="text-center py-12">
          <FuelIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No fuel records found</h3>
          <p className="mt-1 text-sm text-gray-500">Add fuel records to track consumption and costs.</p>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {editingRecord ? 'Edit Fuel Record' : 'Add Fuel Record'}
            </h2>
            
            <form onSubmit={handleSaveRecord} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Vehicle
                </label>
                <select
                  value={formData.vehicle_plate}
                  onChange={(e) => setFormData(prev => ({ ...prev, vehicle_plate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="VH001">VH001 - Toyota Camry</option>
                  <option value="VH002">VH002 - Honda Civic</option>
                  <option value="VH003">VH003 - Ford Focus</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fuel Type
                </label>
                <select
                  value={formData.fuel_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, fuel_type: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="Gasoline">Gasoline</option>
                  <option value="Diesel">Diesel</option>
                  <option value="Electric">Electric</option>
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Quantity (L)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={formData.quantity}
                    onChange={(e) => setFormData(prev => ({ ...prev, quantity: parseFloat(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cost per Liter
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.cost_per_liter}
                    onChange={(e) => setFormData(prev => ({ ...prev, cost_per_liter: parseFloat(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Odometer Reading (km)
                </label>
                <input
                  type="number"
                  value={formData.odometer_reading}
                  onChange={(e) => setFormData(prev => ({ ...prev, odometer_reading: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gas Station
                </label>
                <input
                  type="text"
                  value={formData.station}
                  onChange={(e) => setFormData(prev => ({ ...prev, station: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Driver Name
                </label>
                <input
                  type="text"
                  value={formData.driver_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, driver_name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-sm text-gray-600">
                  Total Cost: <span className="font-bold text-gray-900">
                    ${(formData.quantity * formData.cost_per_liter).toFixed(2)}
                  </span>
                </div>
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {saving ? 'Saving...' : (editingRecord ? 'Update' : 'Add')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Fuel;