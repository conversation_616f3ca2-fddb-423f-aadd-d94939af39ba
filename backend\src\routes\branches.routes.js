const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth.middleware');
const {
  getAllBranches,
  getBranchById,
  createBranch,
  updateBranch,
  deleteBranch
} = require('../controllers/branches.controller');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all branches
router.get('/', getAllBranches);

// Get branch by ID
router.get('/:id', getBranchById);

// Create new branch (Admin and above)
router.post('/', requireRole(['Super Admin', 'Admin']), createBranch);

// Update branch (Admin and above)
router.put('/:id', requireRole(['Super Admin', 'Admin']), updateBranch);

// Delete branch (Super Admin only)
router.delete('/:id', requireRole(['Super Admin']), deleteBranch);

module.exports = router;