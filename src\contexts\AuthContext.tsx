import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { supabase, supabaseAPI } from '../services/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';

interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  branch_id?: string;
  name: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (email: string, password: string, name: string, role?: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastFocusTime, setLastFocusTime] = useState(Date.now());

  // Use refs to avoid circular dependencies
  const loadingRef = useRef(loading);
  const isInitializedRef = useRef(isInitialized);
  const userRef = useRef(user);
  const lastFocusTimeRef = useRef(lastFocusTime);

  // Update refs when state changes
  useEffect(() => { loadingRef.current = loading; }, [loading]);
  useEffect(() => { isInitializedRef.current = isInitialized; }, [isInitialized]);
  useEffect(() => { userRef.current = user; }, [user]);
  useEffect(() => { lastFocusTimeRef.current = lastFocusTime; }, [lastFocusTime]);

  // Debug loading state changes
  const setLoadingWithLog = useCallback((newLoading: boolean, reason?: string) => {
    // Prevent loading state from being set to true if we just focused the tab and user is authenticated
    const timeSinceFocus = Date.now() - lastFocusTimeRef.current;
    if (newLoading && isInitializedRef.current && userRef.current && timeSinceFocus < 3000) {
      console.log(`🚫 Preventing loading state - tab focus detected (${timeSinceFocus}ms ago), user already authenticated`);
      return;
    }

    console.log(`🔄 Loading state change: ${loadingRef.current} -> ${newLoading}${reason ? ` (${reason})` : ''}`);
    setLoading(newLoading);
  }, []);

  // Move loadUserProfile outside useEffect to make it stable
  const loadUserProfile = useCallback(async (supabaseUser: SupabaseUser) => {
    try {
      setLoadingWithLog(true, 'loadUserProfile start');

      // Always create a user from auth data immediately to prevent loading issues
      const fallbackUser = {
        id: supabaseUser.id,
        username: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User',
        email: supabaseUser.email || '',
        role: supabaseUser.user_metadata?.role || 'Admin', // Default to Admin for better access
        name: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User'
      };

      if (!supabase) {
        // In demo mode, just use the fallback user
        console.log('🎭 Demo mode: Creating user from auth data');
        setUser(fallbackUser);
        return;
      }

      // Set the fallback user immediately to prevent loading issues
      setUser(fallbackUser);
      console.log('✅ User authenticated with fallback data, attempting to load full profile...');

      // Try to get existing user profile in the background (non-blocking)
      try {
        const { data: existingUser, error: fetchError } = await supabase
          .from('users')
          .select('*')
          .eq('id', supabaseUser.id)
          .single();

        if (existingUser && !fetchError) {
          console.log('✅ Full user profile loaded successfully, updating user data');
          setUser(existingUser);
        } else {
          console.log('ℹ️ No database profile found, keeping fallback user data');
        }
      } catch (error) {
        console.warn('Database profile fetch failed, keeping fallback user data:', error);
        // User is already set to fallback, so no action needed
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error in loadUserProfile:', errorMessage);

      // Ensure we always have a user set
      if (!userRef.current) {
        setUser({
          id: supabaseUser.id,
          username: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User',
          email: supabaseUser.email || '',
          role: 'Admin',
          name: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User'
        });
      }
    } finally {
      setLoadingWithLog(false, 'loadUserProfile complete');
      setIsInitialized(true);
    }
  }, [setLoadingWithLog]);

  useEffect(() => {
    let mounted = true;
    let authSubscription: any = null;

    // Safety timeout to ensure loading never stays true indefinitely (reduced due to connectivity issues)
    const safetyTimeout = setTimeout(() => {
      if (mounted && loadingRef.current) {
        console.warn('🚨 Safety timeout triggered - forcing loading to false after 8 seconds');
        setLoadingWithLog(false, 'safety timeout');
        setIsInitialized(true);

        // Create a fallback demo user if no user is set
        if (!userRef.current) {
          console.log('🔄 Creating fallback demo user due to timeout');
          setUser({
            id: 'demo-user',
            username: 'Demo User',
            email: '<EMAIL>',
            role: 'Admin',
            name: 'Demo User'
          });
        }
      }
    }, 8000); // 8 second safety timeout

    // Handle page visibility changes to prevent re-authentication on tab focus
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const newFocusTime = Date.now();
        setLastFocusTime(newFocusTime);
        lastFocusTimeRef.current = newFocusTime;
        if (isInitializedRef.current && userRef.current) {
          console.log('👁️ Tab became visible - user already authenticated, tracking focus time');
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Also track window focus events
    const handleWindowFocus = () => {
      const newFocusTime = Date.now();
      setLastFocusTime(newFocusTime);
      lastFocusTimeRef.current = newFocusTime;
      if (isInitializedRef.current && userRef.current) {
        console.log('👁️ Window focused - user already authenticated, tracking focus time');
      }
    };

    window.addEventListener('focus', handleWindowFocus);

    // Check active sessions and sets the user
    const initializeAuth = async () => {
      try {
        if (!supabase) {
          // Demo mode - check for demo user in localStorage
          console.log('Running in demo mode - Supabase not configured');
          const demoUser = localStorage.getItem('demo_user');
          if (demoUser && mounted) {
            try {
              const parsedUser = JSON.parse(demoUser);
              setUser(parsedUser);
              console.log('Demo user loaded from localStorage:', parsedUser.name);
            } catch (parseError) {
              console.warn('Invalid demo user data in localStorage, clearing it');
              localStorage.removeItem('demo_user');
            }
          } else {
            console.log('No demo user found in localStorage - user needs to login with demo credentials');
          }
          if (mounted) {
            setLoadingWithLog(false, 'demo mode initialized');
            setIsInitialized(true);
          }
          return;
        }

        // Only attempt Supabase session check if supabase is available
        console.log('🔐 Initializing Supabase authentication...');

        // Set timeout to 10 seconds - faster timeout for better UX
        const sessionPromise = supabaseAPI.auth.getSession();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session check timeout after 10 seconds')), 10000)
        );

        let sessionResult;
        try {
          sessionResult = await Promise.race([sessionPromise, timeoutPromise]) as any;
        } catch (timeoutError) {
          console.warn('⚠️ Session check timed out, falling back to demo mode');
          // Force demo mode on timeout
          const demoUser = localStorage.getItem('demo_user');
          if (demoUser && mounted) {
            try {
              const parsedUser = JSON.parse(demoUser);
              setUser(parsedUser);
              console.log('🔄 Using demo user due to timeout:', parsedUser.name);
            } catch (parseError) {
              console.warn('Invalid demo user data, clearing localStorage');
              localStorage.removeItem('demo_user');
            }
          }
          if (mounted) {
            setLoadingWithLog(false, 'session timeout fallback');
            setIsInitialized(true);
          }
          return;
        }

        const { data: { session }, error } = sessionResult;

        if (session?.user && !error && mounted) {
          console.log('✅ Valid session found, loading user profile...');
          await loadUserProfile(session.user);
        } else if (error) {
          console.log('ℹ️ No existing session found (normal for new users)');
          if (mounted) {
            setLoadingWithLog(false, 'no existing session');
            setIsInitialized(true);
          }
        } else {
          console.log('ℹ️ No active session found - user needs to login');
          if (mounted) {
            setLoadingWithLog(false, 'no active session');
            setIsInitialized(true);
          }
        }

        // Set up auth state listener only after initial session check
        if (mounted && supabase) {
          console.log('👂 Setting up authentication state listener...');
          const { data: { subscription } } = supabaseAPI.auth.onAuthStateChange(async (event, session) => {
            if (!mounted) return;



            // Skip token refresh events to prevent unnecessary reloads
            if (event === 'TOKEN_REFRESHED') {
              console.log('🔄 Token refreshed - no action needed');
              return;
            }

            // Only log important auth events to reduce noise
            if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
              console.log(`🔄 Auth event: ${event}`);
            }

            try {
              if (session?.user) {
                // Only reload profile on actual sign in, not on token refresh or tab focus
                if (event === 'SIGNED_IN') {
                  console.log('✅ User signed in, loading profile...');
                  await loadUserProfile(session.user);
                } else if (isInitialized && user && user.id === session.user.id) {
                  // User is already loaded and it's the same user, skip reload
                  console.log('👁️ Same user session detected, skipping profile reload');
                  return;
                } else {
                  // Different user or not initialized yet
                  await loadUserProfile(session.user);
                }
              } else {
                if (event === 'SIGNED_OUT') {
                  console.log('👋 User signed out, clearing state');
                  localStorage.removeItem('demo_user');
                }
                setUser(null);
                setLoadingWithLog(false, 'user signed out');
                setIsInitialized(true);
              }
            } catch (error) {
              console.error('❌ Error in auth state change handler:', error);
              // Clear user state on error to prevent infinite loading
              setUser(null);
              if (event === 'SIGNED_OUT') {
                localStorage.removeItem('demo_user');
              }
              setLoadingWithLog(false, 'auth state change error');
              setIsInitialized(true);
            }
          });

          authSubscription = subscription;
        }
      } catch (error: any) {
        if (!mounted) return;

        // Handle timeout errors gracefully
        if (error.message?.includes('timeout')) {
          console.warn('⚠️ Session check timed out - may indicate network issues or Supabase unavailability');
          // In case of timeout, check if we're actually in demo mode
          const demoUser = localStorage.getItem('demo_user');
          if (demoUser) {
            try {
              const parsedUser = JSON.parse(demoUser);
              setUser(parsedUser);
              console.log('🔄 Fallback to demo user due to timeout:', parsedUser.name);
            } catch (parseError) {
              console.warn('Invalid demo user data, clearing localStorage');
              localStorage.removeItem('demo_user');
            }
          } else {
            console.log('ℹ️ No demo user available - user needs to login');
          }
        } else {
          console.error('❌ Unexpected error during authentication:', error.message);
          setUser(null);
          localStorage.removeItem('demo_user');
        }

        setLoadingWithLog(false, 'auth initialization error');
        setIsInitialized(true);
      }
    };

    initializeAuth();

    // Cleanup function
    return () => {
      mounted = false;
      clearTimeout(safetyTimeout);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleWindowFocus);
      if (authSubscription) {
        console.log('🧹 Cleaning up authentication listener');
        authSubscription.unsubscribe();
      }
    };
  }, [loadUserProfile]);



  const register = async (email: string, password: string, name: string, role: string = 'Driver') => {
    try {
      setLoadingWithLog(true, 'register start');
      
      // Validate input
      if (!email || !password || !name) {
        return { success: false, error: 'All fields are required' };
      }

      if (password.length < 6) {
        return { success: false, error: 'Password must be at least 6 characters long' };
      }

      if (!/\S+@\S+\.\S+/.test(email)) {
        return { success: false, error: 'Please enter a valid email address' };
      }

      if (!supabase) {
        // Demo mode - simulate registration
        const demoUser = {
          id: 'demo-' + Date.now(),
          username: name,
          email,
          role,
          name
        };
        setUser(demoUser);
        localStorage.setItem('demo_user', JSON.stringify(demoUser));
        return { success: true };
      }

      // Sign up with Supabase Auth
      const { data, error } = await supabaseAPI.auth.signUp(email, password, {
        name,
        role
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        // Create user profile in users table
        const userProfile = {
          id: data.user.id,
          auth_id: data.user.id,
          name,
          email,
          role,
          status: 'Active' as const
        };

        const { error: profileError } = await supabaseAPI.users.create(userProfile);
        
        if (profileError) {
          console.error('Error creating user profile:', profileError);
          // Continue anyway as the auth user was created
        }

        return { success: true };
      }

      return { success: false, error: 'Registration failed' };
    } catch (error: any) {
      console.error('Registration error:', error);
      return { success: false, error: error.message || 'Registration failed' };
    } finally {
      setLoadingWithLog(false, 'register complete');
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setLoadingWithLog(true, 'login start');

      // Validate input
      if (!email || !password) {
        return { success: false, error: 'Email and password are required' };
      }

      // Check for demo credentials first
      if (email === '<EMAIL>' && password === 'password') {
        console.log('Demo login detected');
        const demoUser = {
          id: 'demo-admin',
          username: 'admin',
          email: '<EMAIL>',
          role: 'Super Admin',
          name: 'Demo Admin'
        };
        setUser(demoUser);
        localStorage.setItem('demo_user', JSON.stringify(demoUser));
        console.log('Demo user logged in successfully');
        return { success: true };
      }

      if (!supabase) {
        console.log('Supabase not configured, only demo login available');
        return { success: false, error: 'Invalid credentials. Try demo login: <EMAIL> / password' };
      }

      // Try Supabase authentication
      const { data, error } = await supabaseAPI.auth.signIn(email, password);

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        await loadUserProfile(data.user);
        return { success: true };
      }

      return { success: false, error: 'Login failed' };
    } catch (error: any) {
      console.error('Login error:', error);
      return { success: false, error: error.message || 'Login failed' };
    } finally {
      setLoadingWithLog(false, 'login complete');
    }
  };

  const logout = async () => {
    try {
      setLoadingWithLog(true, 'logout start');
      
      if (supabase) {
        const { error } = await supabaseAPI.auth.signOut();
        if (error) {
          console.error('Logout error:', error);
        }
      }
      
      setUser(null);
      localStorage.removeItem('demo_user');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoadingWithLog(false, 'logout complete');
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    loading
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};