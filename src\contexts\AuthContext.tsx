import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase, supabaseAPI } from '../services/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';

interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  branch_id?: string;
  name: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (email: string, password: string, name: string, role?: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check active sessions and sets the user
    const getSession = async () => {
      try {
        if (!supabase) {
          // Demo mode - check for demo user in localStorage
          console.log('Running in demo mode - checking localStorage for demo user');
          const demoUser = localStorage.getItem('demo_user');
          if (demoUser) {
            try {
              const parsedUser = JSON.parse(demoUser);
              setUser(parsedUser);
              console.log('Demo user loaded:', parsedUser.name);
            } catch (parseError) {
              console.warn('Invalid demo user data in localStorage, clearing it');
              localStorage.removeItem('demo_user');
            }
          }
          setLoading(false);
          return;
        }

        // Only attempt Supabase session check if supabase is available
        console.log('Checking Supabase session...');

        // Increase timeout to 30 seconds for slower connections
        const sessionPromise = supabaseAPI.auth.getSession();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session check timeout after 30 seconds')), 30000)
        );

        const { data: { session }, error } = await Promise.race([sessionPromise, timeoutPromise]) as any;

        if (session?.user && !error) {
          console.log('Valid session found, loading user profile...');
          await loadUserProfile(session.user);
        } else if (error) {
          console.warn('Session error (this is normal for new users):', error.message);
        } else {
          console.log('No active session found');
        }
      } catch (error: any) {
        // Only log timeout errors as warnings, not errors
        if (error.message?.includes('timeout')) {
          console.warn('Session check timed out - this may indicate network issues or Supabase unavailability');
          // In case of timeout, check if we're actually in demo mode
          const demoUser = localStorage.getItem('demo_user');
          if (demoUser) {
            try {
              const parsedUser = JSON.parse(demoUser);
              setUser(parsedUser);
              console.log('Fallback to demo user due to timeout:', parsedUser.name);
            } catch (parseError) {
              localStorage.removeItem('demo_user');
            }
          }
        } else {
          console.error('Unexpected error getting session:', error);
        }

        // Don't clear user state on timeout - user might be in demo mode
        if (!error.message?.includes('timeout')) {
          setUser(null);
          localStorage.removeItem('demo_user');
        }
      } finally {
        setLoading(false);
      }
    };

    getSession();

    // Listen for changes on auth state (logged in, signed out, etc.)
    // Only set up auth state listener if Supabase is available
    if (supabase) {
      console.log('Setting up Supabase auth state listener...');
      const { data: { subscription } } = supabaseAPI.auth.onAuthStateChange(async (event, session) => {
        console.log('Auth state change event:', event);
        try {
          if (session?.user) {
            console.log('User session detected, loading profile...');
            await loadUserProfile(session.user);
          } else {
            console.log('No user session, clearing user state');
            setUser(null);
            // Only remove demo_user if this is a real sign out, not just missing session
            if (event === 'SIGNED_OUT') {
              localStorage.removeItem('demo_user');
            }
          }
        } catch (error) {
          console.error('Error in auth state change handler:', error);
          // Clear user state on error to prevent infinite loading
          setUser(null);
          if (event === 'SIGNED_OUT') {
            localStorage.removeItem('demo_user');
          }
        } finally {
          // Always ensure loading is set to false
          setLoading(false);
        }
      });

      return () => {
        console.log('Cleaning up auth state listener');
        subscription.unsubscribe();
      };
    } else {
      console.log('Supabase not available - skipping auth state listener setup');
    }
  }, []);

  const loadUserProfile = async (supabaseUser: SupabaseUser) => {
    try {
      // Try to get user profile from users table
      const { data: userProfile, error } = await supabaseAPI.users.getById(supabaseUser.id);

      if (userProfile && !error) {
        setUser({
          id: userProfile.id,
          username: userProfile.name,
          email: userProfile.email,
          role: userProfile.role,
          branch_id: userProfile.branch_id || undefined,
          name: userProfile.name
        });
      } else {
        // If no profile exists, create one with default values
        const newUserProfile = {
          id: supabaseUser.id,
          auth_id: supabaseUser.id,
          name: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User',
          email: supabaseUser.email!,
          role: supabaseUser.user_metadata?.role || 'Driver',
          status: 'Active' as const
        };

        const { data: createdProfile, error: createError } = await supabaseAPI.users.create(newUserProfile);

        if (createdProfile && !createError) {
          setUser({
            id: createdProfile.id,
            username: createdProfile.name,
            email: createdProfile.email,
            role: createdProfile.role,
            branch_id: createdProfile.branch_id || undefined,
            name: createdProfile.name
          });
        } else {
          // If profile creation fails, create a fallback user from auth data
          console.warn('Failed to create user profile, using fallback user data');
          setUser({
            id: supabaseUser.id,
            username: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User',
            email: supabaseUser.email || '',
            role: supabaseUser.user_metadata?.role || 'Driver',
            name: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User'
          });
        }
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      // Create a fallback user from auth data to prevent infinite loading
      setUser({
        id: supabaseUser.id,
        username: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User',
        email: supabaseUser.email || '',
        role: supabaseUser.user_metadata?.role || 'Driver',
        name: supabaseUser.user_metadata?.name || supabaseUser.email?.split('@')[0] || 'User'
      });
    }
  };

  const register = async (email: string, password: string, name: string, role: string = 'Driver') => {
    try {
      setLoading(true);
      
      // Validate input
      if (!email || !password || !name) {
        return { success: false, error: 'All fields are required' };
      }

      if (password.length < 6) {
        return { success: false, error: 'Password must be at least 6 characters long' };
      }

      if (!/\S+@\S+\.\S+/.test(email)) {
        return { success: false, error: 'Please enter a valid email address' };
      }

      if (!supabase) {
        // Demo mode - simulate registration
        const demoUser = {
          id: 'demo-' + Date.now(),
          username: name,
          email,
          role,
          name
        };
        setUser(demoUser);
        localStorage.setItem('demo_user', JSON.stringify(demoUser));
        return { success: true };
      }

      // Sign up with Supabase Auth
      const { data, error } = await supabaseAPI.auth.signUp(email, password, {
        name,
        role
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        // Create user profile in users table
        const userProfile = {
          id: data.user.id,
          auth_id: data.user.id,
          name,
          email,
          role,
          status: 'Active' as const
        };

        const { error: profileError } = await supabaseAPI.users.create(userProfile);
        
        if (profileError) {
          console.error('Error creating user profile:', profileError);
          // Continue anyway as the auth user was created
        }

        return { success: true };
      }

      return { success: false, error: 'Registration failed' };
    } catch (error: any) {
      console.error('Registration error:', error);
      return { success: false, error: error.message || 'Registration failed' };
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);

      // Validate input
      if (!email || !password) {
        return { success: false, error: 'Email and password are required' };
      }

      // Check for demo credentials first
      if (email === '<EMAIL>' && password === 'password') {
        console.log('Demo login detected');
        const demoUser = {
          id: 'demo-admin',
          username: 'admin',
          email: '<EMAIL>',
          role: 'Super Admin',
          name: 'Demo Admin'
        };
        setUser(demoUser);
        localStorage.setItem('demo_user', JSON.stringify(demoUser));
        console.log('Demo user logged in successfully');
        return { success: true };
      }

      if (!supabase) {
        console.log('Supabase not configured, only demo login available');
        return { success: false, error: 'Invalid credentials. Try demo login: <EMAIL> / password' };
      }

      // Try Supabase authentication
      const { data, error } = await supabaseAPI.auth.signIn(email, password);

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        await loadUserProfile(data.user);
        return { success: true };
      }

      return { success: false, error: 'Login failed' };
    } catch (error: any) {
      console.error('Login error:', error);
      return { success: false, error: error.message || 'Login failed' };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      
      if (supabase) {
        const { error } = await supabaseAPI.auth.signOut();
        if (error) {
          console.error('Logout error:', error);
        }
      }
      
      setUser(null);
      localStorage.removeItem('demo_user');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    loading
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};