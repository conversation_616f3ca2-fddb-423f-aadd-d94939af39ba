import { useState, useEffect, useCallback, useRef } from 'react';
import { supabaseAPI } from '../services/supabase';
import { Notification } from '../types';

interface UseNotificationsOptions {
  realTime?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  filters?: {
    type?: string;
    severity?: string;
    read?: boolean;
  };
  pagination?: {
    page?: number;
    limit?: number;
  };
}

export const useNotifications = (options: UseNotificationsOptions = {}) => {
  const {
    realTime = true,
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    filters,
    pagination
  } = options;

  const [data, setData] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [count, setCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const subscriptionRef = useRef<any>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchNotifications = useCallback(async () => {
    console.log('🔔 fetchNotifications called - using demo data immediately');

    // Set demo data immediately to prevent infinite loading
    const demoData = [
      {
        id: 'demo-1',
        user_id: 'demo-user',
        title: 'Welcome to Fleet Management',
        message: 'Your account has been successfully created! You can now manage your fleet efficiently.',
        type: 'success' as const,
        severity: 'low' as const,
        read: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'demo-2',
        user_id: 'demo-user',
        title: 'System Maintenance Scheduled',
        message: 'Scheduled maintenance will occur tonight from 2-4 AM. Some features may be temporarily unavailable.',
        type: 'warning' as const,
        severity: 'medium' as const,
        read: false,
        created_at: new Date(Date.now() - 3600000).toISOString(),
        updated_at: new Date(Date.now() - 3600000).toISOString()
      },
      {
        id: 'demo-3',
        user_id: 'demo-user',
        title: 'Vehicle Maintenance Due',
        message: 'Vehicle ABC-123 is due for maintenance. Please schedule an appointment.',
        type: 'info' as const,
        severity: 'high' as const,
        read: true,
        created_at: new Date(Date.now() - 7200000).toISOString(),
        updated_at: new Date(Date.now() - 7200000).toISOString()
      }
    ];

    // Set demo data immediately
    setData(demoData);
    setCount(demoData.length);
    setHasMore(false);
    setLastUpdated(new Date());
    setError(null);
    setLoading(false);

    console.log('🔔 Demo notifications set successfully:', demoData.length);

    // Try to fetch real data in background (non-blocking)
    try {
      setError('Database connection unavailable - using demo data');

      // Simulate a quick attempt to connect to database
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database timeout - using demo data')), 1000)
      );

      let result;
      if (pagination) {
        const fetchPromise = supabaseAPI.notifications.getPaginated(
          pagination.page || 1,
          pagination.limit || 20,
          filters
        );
        result = await Promise.race([fetchPromise, timeoutPromise]) as any;
      } else {
        const fetchPromise = supabaseAPI.notifications.getAll();
        result = await Promise.race([fetchPromise, timeoutPromise]) as any;
      }

      // If we get here, database is working
      if (result && !result.error && result.data) {
        console.log('🔔 Real notifications loaded successfully:', result.data.length);
        setData(result.data);
        setCount(result.count || result.data.length);
        setHasMore(result.hasMore || false);
        setLastUpdated(new Date());
        setError(null);
      }
    } catch (err: any) {
      // Keep demo data, just log the error
      console.log('🔔 Database unavailable, keeping demo data:', err.message);
      setError('Database connection unavailable - using demo data');
    }
  }, [filters, pagination]);

  // Real-time subscription setup - DISABLED to prevent infinite loading
  useEffect(() => {
    console.log('🔔 Real-time subscriptions disabled to prevent infinite loading');
    // Real-time subscriptions are disabled for stability
    return () => {
      if (subscriptionRef.current) {
        console.log('🔔 Cleaning up any existing subscription');
        subscriptionRef.current.unsubscribe();
      }
    };
  }, []);

  // Auto-refresh setup - DISABLED to prevent infinite loading
  useEffect(() => {
    console.log('🔔 Auto-refresh disabled to prevent infinite loading');
    // Auto-refresh is disabled for stability
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Initial fetch
  useEffect(() => {
    console.log('🔔 useNotifications: Starting initial fetch');
    fetchNotifications();
  }, [fetchNotifications]);

  const markAsRead = async (id: string) => {
    try {
      const result = await supabaseAPI.notifications.markAsRead(id);
      if (!result.error) {
        setData(prev => prev.map(notification => 
          notification.id === id ? { ...notification, read: true } : notification
        ));
        console.log('🔔 Notification marked as read:', id);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Mark as read error:', error);
      return { data: null, error };
    }
  };

  const deleteNotification = async (id: string) => {
    try {
      const result = await supabaseAPI.notifications.delete(id);
      if (!result.error) {
        setData(prev => prev.filter(notification => notification.id !== id));
        setCount(prev => Math.max(0, prev - 1));
        console.log('🔔 Notification deleted:', id);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Delete notification error:', error);
      return { data: null, error };
    }
  };

  const markAllAsRead = async () => {
    try {
      const result = await supabaseAPI.notifications.markAllAsRead();
      if (!result.error) {
        setData(prev => prev.map(notification => ({ ...notification, read: true })));
        console.log('🔔 All notifications marked as read');
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Mark all as read error:', error);
      return { data: null, error };
    }
  };

  const markMultipleAsRead = async (ids: string[]) => {
    try {
      const result = await supabaseAPI.notifications.markMultipleAsRead(ids);
      if (!result.error) {
        setData(prev => prev.map(notification => 
          ids.includes(notification.id) ? { ...notification, read: true } : notification
        ));
        console.log('🔔 Multiple notifications marked as read:', ids);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Mark multiple as read error:', error);
      return { data: null, error };
    }
  };

  const deleteMultiple = async (ids: string[]) => {
    try {
      const result = await supabaseAPI.notifications.deleteMultiple(ids);
      if (!result.error) {
        setData(prev => prev.filter(notification => !ids.includes(notification.id)));
        setCount(prev => Math.max(0, prev - ids.length));
        console.log('🔔 Multiple notifications deleted:', ids);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Delete multiple error:', error);
      return { data: null, error };
    }
  };

  const createNotification = async (notification: Omit<Notification, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const result = await supabaseAPI.notifications.create(notification);
      if (!result.error && result.data) {
        // If real-time is disabled, manually add to state
        if (!realTime) {
          setData(prev => [result.data!, ...prev]);
          setCount(prev => prev + 1);
        }
        console.log('🔔 Notification created:', result.data);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Create notification error:', error);
      return { data: null, error };
    }
  };

  return {
    data,
    loading,
    error,
    count,
    hasMore,
    lastUpdated,
    refetch: fetchNotifications,
    markAsRead,
    deleteNotification,
    markAllAsRead,
    markMultipleAsRead,
    deleteMultiple,
    createNotification
  };
};
