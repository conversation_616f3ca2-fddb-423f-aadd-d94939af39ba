import { useState, useEffect, useCallback, useRef } from 'react';
import { supabaseAPI } from '../services/supabase';
import { Notification } from '../types';

interface UseNotificationsOptions {
  realTime?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  filters?: {
    type?: string;
    severity?: string;
    read?: boolean;
  };
  pagination?: {
    page?: number;
    limit?: number;
  };
}

export const useNotifications = (options: UseNotificationsOptions = {}) => {
  const {
    realTime = true,
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    filters,
    pagination
  } = options;

  const [data, setData] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [count, setCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const subscriptionRef = useRef<any>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Add timeout to prevent infinite loading (reduced to 3 seconds)
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout after 3 seconds')), 3000)
      );

      let result;
      if (pagination) {
        const fetchPromise = supabaseAPI.notifications.getPaginated(
          pagination.page || 1,
          pagination.limit || 20,
          filters
        );
        result = await Promise.race([fetchPromise, timeoutPromise]) as any;
        setCount(result.count);
        setHasMore(result.hasMore);
      } else {
        const fetchPromise = supabaseAPI.notifications.getAll();
        result = await Promise.race([fetchPromise, timeoutPromise]) as any;
        setCount(result.data?.length || 0);
        setHasMore(false);
      }

      if (result.error) {
        setError(result.error.message);
        console.error('🔔 Notifications API error:', result.error);
      } else {
        setData(result.data || []);
        setLastUpdated(new Date());
        console.log('🔔 Notifications loaded successfully:', result.data?.length || 0);
      }
    } catch (err: any) {
      setError(err.message);
      console.error('🔔 Fetch notifications error:', err);

      // Fallback to demo data on error
      if (err.message.includes('timeout') || err.message.includes('network')) {
        console.warn('🔔 Using demo notifications due to connectivity issues');
        setData([
          {
            id: 'demo-1',
            user_id: 'demo-user',
            title: 'Welcome to Fleet Management',
            message: 'Your account has been successfully created!',
            type: 'success' as const,
            severity: 'low' as const,
            read: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 'demo-2',
            user_id: 'demo-user',
            title: 'System Maintenance',
            message: 'Scheduled maintenance will occur tonight from 2-4 AM.',
            type: 'warning' as const,
            severity: 'medium' as const,
            read: false,
            created_at: new Date(Date.now() - 3600000).toISOString(),
            updated_at: new Date(Date.now() - 3600000).toISOString()
          }
        ]);
        setCount(2);
        setError(null); // Clear error since we have fallback data
      }
    } finally {
      setLoading(false);
    }
  }, [filters, pagination]);

  // Real-time subscription setup (only if not in demo mode)
  useEffect(() => {
    if (realTime && !error) { // Don't setup subscription if we have connectivity errors
      console.log('🔔 Setting up real-time notifications subscription');

      try {
        subscriptionRef.current = supabaseAPI.notifications.subscribe((payload) => {
          console.log('🔔 Real-time notification update received:', payload);
        
        switch (payload.eventType) {
          case 'INSERT':
            setData(prev => [payload.new, ...prev]);
            setCount(prev => prev + 1);
            break;
          case 'UPDATE':
            setData(prev => prev.map(notification => 
              notification.id === payload.new.id ? payload.new : notification
            ));
            break;
          case 'DELETE':
            setData(prev => prev.filter(notification => notification.id !== payload.old.id));
            setCount(prev => Math.max(0, prev - 1));
            break;
        }
          setLastUpdated(new Date());
        });
      } catch (subscriptionError) {
        console.warn('🔔 Failed to setup real-time subscription, continuing with manual refresh only');
      }

      return () => {
        if (subscriptionRef.current) {
          console.log('🔔 Cleaning up notifications subscription');
          subscriptionRef.current.unsubscribe();
        }
      };
    }
  }, [realTime, error]);

  // Auto-refresh setup (only if not in demo mode due to errors)
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0 && !error) { // Don't auto-refresh if we have connectivity errors
      intervalRef.current = setInterval(() => {
        console.log('🔔 Auto-refreshing notifications');
        fetchNotifications();
      }, refreshInterval);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval, fetchNotifications, error]);

  // Initial fetch
  useEffect(() => {
    console.log('🔔 useNotifications: Starting initial fetch');
    fetchNotifications();
  }, [fetchNotifications]);

  const markAsRead = async (id: string) => {
    try {
      const result = await supabaseAPI.notifications.markAsRead(id);
      if (!result.error) {
        setData(prev => prev.map(notification => 
          notification.id === id ? { ...notification, read: true } : notification
        ));
        console.log('🔔 Notification marked as read:', id);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Mark as read error:', error);
      return { data: null, error };
    }
  };

  const deleteNotification = async (id: string) => {
    try {
      const result = await supabaseAPI.notifications.delete(id);
      if (!result.error) {
        setData(prev => prev.filter(notification => notification.id !== id));
        setCount(prev => Math.max(0, prev - 1));
        console.log('🔔 Notification deleted:', id);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Delete notification error:', error);
      return { data: null, error };
    }
  };

  const markAllAsRead = async () => {
    try {
      const result = await supabaseAPI.notifications.markAllAsRead();
      if (!result.error) {
        setData(prev => prev.map(notification => ({ ...notification, read: true })));
        console.log('🔔 All notifications marked as read');
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Mark all as read error:', error);
      return { data: null, error };
    }
  };

  const markMultipleAsRead = async (ids: string[]) => {
    try {
      const result = await supabaseAPI.notifications.markMultipleAsRead(ids);
      if (!result.error) {
        setData(prev => prev.map(notification => 
          ids.includes(notification.id) ? { ...notification, read: true } : notification
        ));
        console.log('🔔 Multiple notifications marked as read:', ids);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Mark multiple as read error:', error);
      return { data: null, error };
    }
  };

  const deleteMultiple = async (ids: string[]) => {
    try {
      const result = await supabaseAPI.notifications.deleteMultiple(ids);
      if (!result.error) {
        setData(prev => prev.filter(notification => !ids.includes(notification.id)));
        setCount(prev => Math.max(0, prev - ids.length));
        console.log('🔔 Multiple notifications deleted:', ids);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Delete multiple error:', error);
      return { data: null, error };
    }
  };

  const createNotification = async (notification: Omit<Notification, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const result = await supabaseAPI.notifications.create(notification);
      if (!result.error && result.data) {
        // If real-time is disabled, manually add to state
        if (!realTime) {
          setData(prev => [result.data!, ...prev]);
          setCount(prev => prev + 1);
        }
        console.log('🔔 Notification created:', result.data);
      }
      return result;
    } catch (error: any) {
      console.error('🔔 Create notification error:', error);
      return { data: null, error };
    }
  };

  return {
    data,
    loading,
    error,
    count,
    hasMore,
    lastUpdated,
    refetch: fetchNotifications,
    markAsRead,
    deleteNotification,
    markAllAsRead,
    markMultipleAsRead,
    deleteMultiple,
    createNotification
  };
};
