-- Quick Reset Script for Notifications Table
-- Run this in Supabase SQL Editor for a fast reset

-- Drop everything related to notifications
DROP TABLE IF EXISTS public.notifications CASCADE;

-- Create new notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'urgent')),
    read BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Enable RLS
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create policy
CREATE POLICY "Users can manage their own notifications" 
ON public.notifications FOR ALL 
USING (auth.uid() = user_id);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at DESC);

-- Create update trigger
CREATE OR REPLACE FUNCTION update_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_notifications_updated_at
    BEFORE UPDATE ON public.notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_notifications_updated_at();

-- Insert sample data for current user
INSERT INTO public.notifications (user_id, title, message, type, severity, read) 
SELECT 
    auth.uid(),
    title,
    message,
    type,
    severity,
    read
FROM (VALUES 
    ('Welcome to Fleet Management', 'Your account has been successfully created!', 'success', 'low', false),
    ('System Maintenance', 'Scheduled maintenance tonight from 2-4 AM.', 'warning', 'medium', false),
    ('Vehicle Alert', 'Vehicle ABC-123 requires maintenance.', 'error', 'high', false),
    ('New Features', 'Check out our new reporting dashboard.', 'info', 'low', true),
    ('License Expiry', 'Driver license expires in 30 days.', 'warning', 'high', false)
) AS sample_data(title, message, type, severity, read)
WHERE auth.uid() IS NOT NULL;

-- Verify creation
SELECT COUNT(*) as notification_count FROM public.notifications;
