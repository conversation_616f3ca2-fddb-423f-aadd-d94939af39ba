const supabase = require('../utils/supabaseClient');

const getAllBranches = async (req, res) => {
  try {
    const { status } = req.query;
    
    let query = supabase
      .from('branches')
      .select(`
        *,
        manager:users!branches_manager_id_fkey(name, email),
        vehicle_count:vehicles(count),
        driver_count:drivers(count)
      `);

    if (status) {
      query = query.eq('status', status);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch branches',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get branches error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getBranchById = async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        manager:users!branches_manager_id_fkey(name, email, phone),
        vehicles:vehicles(id, license_plate, make, model, vehicle_status),
        drivers:drivers(id, name_en, status, phone)
      `)
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        message: 'Branch not found',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get branch error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const createBranch = async (req, res) => {
  try {
    const branchData = req.body;

    // Validate required fields
    const requiredFields = ['name', 'location'];
    for (const field of requiredFields) {
      if (!branchData[field]) {
        return res.status(400).json({
          success: false,
          message: `${field} is required`
        });
      }
    }

    const { data, error } = await supabase
      .from('branches')
      .insert(branchData)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create branch',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'Branch created successfully',
      data
    });
  } catch (error) {
    console.error('Create branch error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const updateBranch = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.id;
    delete updates.created_at;

    const { data, error } = await supabase
      .from('branches')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update branch',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Branch updated successfully',
      data
    });
  } catch (error) {
    console.error('Update branch error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const deleteBranch = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if branch has associated vehicles or drivers
    const { data: vehicles } = await supabase
      .from('vehicles')
      .select('id')
      .eq('branch_id', id)
      .limit(1);

    const { data: drivers } = await supabase
      .from('drivers')
      .select('id')
      .eq('branch_id', id)
      .limit(1);

    if (vehicles?.length > 0 || drivers?.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete branch with associated vehicles or drivers'
      });
    }

    const { error } = await supabase
      .from('branches')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to delete branch',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Branch deleted successfully'
    });
  } catch (error) {
    console.error('Delete branch error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getAllBranches,
  getBranchById,
  createBranch,
  updateBranch,
  deleteBranch
};