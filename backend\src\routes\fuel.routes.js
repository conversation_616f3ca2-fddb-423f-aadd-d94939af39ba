const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth.middleware');
const {
  getAllFuel,
  getFuelById,
  createFuel,
  updateFuel,
  deleteFuel,
  getFuelStats
} = require('../controllers/fuel.controller');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all fuel records
router.get('/', getAllFuel);

// Get fuel statistics
router.get('/stats', getFuelStats);

// Get fuel record by ID
router.get('/:id', getFuelById);

// Create new fuel record (All authenticated users)
router.post('/', createFuel);

// Update fuel record (Manager and above)
router.put('/:id', requireRole(['Super Admin', 'Admin', 'Manager']), updateFuel);

// Delete fuel record (Admin and above)
router.delete('/:id', requireRole(['Super Admin', 'Admin']), deleteFuel);

module.exports = router;