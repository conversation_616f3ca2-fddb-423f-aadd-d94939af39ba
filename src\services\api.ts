import axios from 'axios';
import { supabaseAPI } from './supabase';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle unauthorized responses
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  login: async (username: string, password: string) => {
    try {
      // Try Supabase authentication first
      const { data, error } = await supabaseAPI.auth.signIn(username, password);
      
      if (data.user && !error) {
        // Get user profile from users table
        const { data: userProfile } = await supabaseAPI.users.getById(data.user.id);
        
        return {
          user: {
            id: data.user.id,
            username: userProfile?.name || data.user.email?.split('@')[0] || 'user',
            email: data.user.email,
            role: userProfile?.role || 'Driver',
            branch_id: userProfile?.branch_id
          },
          token: data.session?.access_token || 'supabase-token'
        };
      }
      
      // Fallback to mock login for demo
      if (username === 'admin' && password === 'password') {
        return {
          user: {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            role: 'Administrator',
            branch_id: 1
          },
          token: 'demo-token-12345'
        };
      }
      
      throw new Error('Invalid credentials');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },
  
  verifyToken: async () => {
    try {
      const { data, error } = await supabaseAPI.auth.getUser();
      if (data.user && !error) {
        const { data: userProfile } = await supabaseAPI.users.getById(data.user.id);
        return {
          id: data.user.id,
          username: userProfile?.name || data.user.email?.split('@')[0] || 'user',
          email: data.user.email,
          role: userProfile?.role || 'Driver',
          branch_id: userProfile?.branch_id
        };
      }
      throw new Error('Invalid token');
    } catch (error) {
      throw error;
    }
  }
};

export const dashboardAPI = {
  getStats: async () => {
    try {
      // Get counts from different tables
      const [vehicles, drivers, branches, maintenance, fuel] = await Promise.all([
        supabaseAPI.vehicles.getAll(),
        supabaseAPI.drivers.getAll(),
        supabaseAPI.branches.getAll(),
        supabaseAPI.maintenance.getAll(),
        supabaseAPI.fuel.getAll()
      ]);

      // Calculate statistics
      const activeVehicles = vehicles.data?.filter(v => v.vehicle_status === 'Active').length || 0;
      const activeDrivers = drivers.data?.filter(d => d.status === 'Active').length || 0;
      const pendingMaintenance = maintenance.data?.filter(m => m.status === 'Scheduled' || m.status === 'In Progress').length || 0;
      
      // Calculate total fuel consumption
      const totalFuelConsumption = fuel.data?.reduce((total, record) => total + (record.quantity || 0), 0) || 0;

      // Calculate alerts (e.g., vehicles due for maintenance, expired licenses)
      let alerts = 0;
      const today = new Date();
      
      // Add maintenance due alerts
      const maintenanceDue = maintenance.data?.filter(m => {
        const nextService = new Date(m.next_service_date);
        return nextService <= today;
      }).length || 0;
      alerts += maintenanceDue;

      // Add driver license expiry alerts
      const expiredLicenses = drivers.data?.filter(d => {
        if (!d.license_expiry) return false;
        const expiryDate = new Date(d.license_expiry);
        return expiryDate <= today;
      }).length || 0;
      alerts += expiredLicenses;

      return {
        totalVehicles: vehicles.data?.length || 0,
        activeVehicles,
        totalDrivers: drivers.data?.length || 0,
        activeDrivers,
        totalBranches: branches.data?.length || 0,
        pendingMaintenance,
        fuelConsumption: Math.round(totalFuelConsumption),
        alerts
      };
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
      throw error;
    }
  }
};

export const vehiclesAPI = {
  getAll: async () => {
    try {
      const { data, error } = await supabaseAPI.vehicles.getAll();
      if (error) throw error;
      
      // Transform data to match frontend interface
      return data?.map(vehicle => ({
        id: vehicle.id,
        plate_number: vehicle.license_plate,
        make: vehicle.make || '',
        model: vehicle.model || '',
        year: vehicle.year || new Date().getFullYear(),
        fuel_type: vehicle.fuel_type,
        status: vehicle.vehicle_status,
        branch_id: vehicle.branch_id,
        branch_name: vehicle.branch?.name,
        mileage: vehicle.current_km,
        last_maintenance: null // This would need to be calculated from maintenance records
      })) || [];
    } catch (error) {
      console.error('Failed to fetch vehicles:', error);
      throw error;
    }
  },
  
  getById: async (id: number) => {
    const response = await api.get(`/vehicles/${id}`);
    return response.data;
  },
  
  create: async (vehicle: any) => {
    try {
      const { data, error } = await supabaseAPI.vehicles.create({
        vehicle_id: vehicle.plate_number,
        license_plate: vehicle.plate_number,
        make: vehicle.make,
        model: vehicle.model,
        year: vehicle.year,
        fuel_type: vehicle.fuel_type,
        vehicle_status: vehicle.status,
        branch_id: vehicle.branch_id,
        current_km: vehicle.mileage
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to create vehicle:', error);
      throw error;
    }
  },
  
  update: async (id: number, vehicle: any) => {
    try {
      const { data, error } = await supabaseAPI.vehicles.update(id.toString(), {
        license_plate: vehicle.plate_number,
        make: vehicle.make,
        model: vehicle.model,
        year: vehicle.year,
        fuel_type: vehicle.fuel_type,
        vehicle_status: vehicle.status,
        branch_id: vehicle.branch_id,
        current_km: vehicle.mileage
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to update vehicle:', error);
      throw error;
    }
  },
  
  delete: async (id: number) => {
    try {
      const { error } = await supabaseAPI.vehicles.delete(id.toString());
      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Failed to delete vehicle:', error);
      throw error;
    }
  }
};

export const driversAPI = {
  getAll: async () => {
    try {
      const { data, error } = await supabaseAPI.drivers.getAll();
      if (error) throw error;
      
      // Transform data to match frontend interface
      return data?.map(driver => ({
        id: driver.id,
        name: driver.name_en,
        license_number: driver.license_number || '',
        phone: driver.phone || '',
        email: driver.email || '',
        status: driver.status,
        branch_id: driver.branch_id,
        branch_name: driver.branch?.name,
        hire_date: driver.hire_date || new Date().toISOString().split('T')[0],
        license_expiry: driver.license_expiry
      })) || [];
    } catch (error) {
      console.error('Failed to fetch drivers:', error);
      throw error;
    }
  },
  
  getById: async (id: number) => {
    const response = await api.get(`/drivers/${id}`);
    return response.data;
  },
  
  create: async (driver: any) => {
    try {
      const { data, error } = await supabaseAPI.drivers.create({
        code: Math.floor(Math.random() * 1000) + 500, // Generate random code
        name_en: driver.name,
        name_ar: driver.name, // For now, use same name
        license_number: driver.license_number,
        phone: driver.phone,
        email: driver.email,
        status: driver.status,
        branch_id: driver.branch_id,
        hire_date: driver.hire_date,
        license_expiry: driver.license_expiry
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to create driver:', error);
      throw error;
    }
  },
  
  update: async (id: number, driver: any) => {
    try {
      const { data, error } = await supabaseAPI.drivers.update(id.toString(), {
        name_en: driver.name,
        name_ar: driver.name,
        license_number: driver.license_number,
        phone: driver.phone,
        email: driver.email,
        status: driver.status,
        branch_id: driver.branch_id,
        hire_date: driver.hire_date,
        license_expiry: driver.license_expiry
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to update driver:', error);
      throw error;
    }
  },
  
  delete: async (id: number) => {
    try {
      const { error } = await supabaseAPI.drivers.delete(id.toString());
      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Failed to delete driver:', error);
      throw error;
    }
  }
};

export const branchesAPI = {
  getAll: async () => {
    try {
      const { data, error } = await supabaseAPI.branches.getAll();
      if (error) throw error;
      
      // Transform data to match frontend interface
      return data?.map(branch => ({
        id: branch.id,
        name: branch.name,
        address: branch.address || branch.location,
        phone: branch.phone || '',
        email: branch.email || '',
        manager_name: branch.manager?.name,
        vehicle_count: branch.vehicle_count || 0,
        driver_count: branch.driver_count || 0,
        status: branch.status
      })) || [];
    } catch (error) {
      console.error('Failed to fetch branches:', error);
      throw error;
    }
  },
  
  getById: async (id: number) => {
    const response = await api.get(`/branches/${id}`);
    return response.data;
  },
  
  create: async (branch: any) => {
    try {
      const { data, error } = await supabaseAPI.branches.create({
        name: branch.name,
        location: branch.address,
        address: branch.address,
        phone: branch.phone,
        email: branch.email,
        status: branch.status
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to create branch:', error);
      throw error;
    }
  },
  
  update: async (id: number, branch: any) => {
    try {
      const { data, error } = await supabaseAPI.branches.update(id.toString(), {
        name: branch.name,
        location: branch.address,
        address: branch.address,
        phone: branch.phone,
        email: branch.email,
        status: branch.status
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to update branch:', error);
      throw error;
    }
  },
  
  delete: async (id: number) => {
    try {
      const { error } = await supabaseAPI.branches.delete(id.toString());
      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Failed to delete branch:', error);
      throw error;
    }
  }
};

export const maintenanceAPI = {
  getAll: async () => {
    try {
      const { data, error } = await supabaseAPI.maintenance.getAll();
      if (error) throw error;
      
      // Transform data to match frontend interface
      return data?.map(maintenance => ({
        id: maintenance.id,
        vehicle_id: maintenance.vehicle_id,
        vehicle_plate: maintenance.vehicle?.license_plate,
        type: maintenance.service_type,
        description: maintenance.description || '',
        cost: maintenance.total_cost,
        scheduled_date: maintenance.scheduled_date || maintenance.service_date,
        completed_date: maintenance.completed_date,
        status: maintenance.status,
        mechanic: maintenance.mechanic,
        next_service: maintenance.next_service_date
      })) || [];
    } catch (error) {
      console.error('Failed to fetch maintenance records:', error);
      throw error;
    }
  },
  
  create: async (maintenance: any) => {
    try {
      const { data, error } = await supabaseAPI.maintenance.create({
        vehicle_id: maintenance.vehicle_id,
        service_type: maintenance.type,
        description: maintenance.description,
        total_cost: maintenance.cost,
        scheduled_date: maintenance.scheduled_date,
        status: maintenance.status,
        mechanic: maintenance.mechanic
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to create maintenance record:', error);
      throw error;
    }
  },
  
  update: async (id: number, maintenance: any) => {
    try {
      const { data, error } = await supabaseAPI.maintenance.update(id.toString(), {
        service_type: maintenance.type,
        description: maintenance.description,
        total_cost: maintenance.cost,
        scheduled_date: maintenance.scheduled_date,
        status: maintenance.status,
        mechanic: maintenance.mechanic,
        completed_date: maintenance.status === 'Completed' ? new Date().toISOString().split('T')[0] : null
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to update maintenance record:', error);
      throw error;
    }
  }
};

export const fuelAPI = {
  getAll: async () => {
    try {
      const { data, error } = await supabaseAPI.fuel.getAll();
      if (error) throw error;
      
      // Transform data to match frontend interface
      return data?.map(fuel => ({
        id: fuel.id,
        vehicle_id: fuel.vehicle_id,
        vehicle_plate: fuel.vehicle?.license_plate,
        fuel_type: fuel.fuel_type,
        quantity: fuel.quantity,
        cost_per_liter: fuel.cost_per_liter || (fuel.amount / fuel.quantity),
        total_cost: fuel.amount,
        odometer_reading: fuel.odometer || 0,
        date: fuel.datetime.split('T')[0],
        station: fuel.station || '',
        driver_name: fuel.driver?.name_en
      })) || [];
    } catch (error) {
      console.error('Failed to fetch fuel records:', error);
      throw error;
    }
  },
  
  create: async (fuel: any) => {
    try {
      const { data, error } = await supabaseAPI.fuel.create({
        vehicle_id: fuel.vehicle_id,
        fuel_type: fuel.fuel_type,
        quantity: fuel.quantity,
        amount: fuel.quantity * fuel.cost_per_liter,
        cost_per_liter: fuel.cost_per_liter,
        odometer: fuel.odometer_reading,
        datetime: new Date(fuel.date).toISOString(),
        station: fuel.station,
        driver_name_en: fuel.driver_name
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to create fuel record:', error);
      throw error;
    }
  }
};

export const reportsAPI = {
  getVehicleReports: async () => {
    const response = await api.get('/reports/vehicles');
    return response.data;
  },
  
  getMaintenanceReports: async () => {
    const response = await api.get('/reports/maintenance');
    return response.data;
  },
  
  getFuelReports: async () => {
    const response = await api.get('/reports/fuel');
    return response.data;
  }
};

export default api;