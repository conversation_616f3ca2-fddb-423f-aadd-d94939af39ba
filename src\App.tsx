import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Vehicles from './pages/Vehicles';
import Drivers from './pages/Drivers';
import Branches from './pages/Branches';
import Maintenance from './pages/Maintenance';
import Fuel from './pages/Fuel';
import Thresholds from './pages/Thresholds';
import Reports from './pages/Reports';
import Notifications from './pages/Notifications';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import PerformanceMonitor from './components/PerformanceMonitor';
import { autoPrefetch, setupIdlePrefetch } from './utils/dataPrefetcher';

// Component to handle route-based prefetching
const PrefetchManager: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // Setup idle prefetching
    const cleanup = setupIdlePrefetch();
    return cleanup;
  }, []);

  useEffect(() => {
    // Prefetch data based on current route
    autoPrefetch(location.pathname);
  }, [location.pathname]);

  return null;
};

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <Router
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <PrefetchManager />
          <div className="min-h-screen bg-gray-50">
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }>
                <Route index element={<Navigate to="/dashboard" replace />} />
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="vehicles" element={<Vehicles />} />
                <Route path="drivers" element={<Drivers />} />
                <Route path="branches" element={<Branches />} />
                <Route path="maintenance" element={<Maintenance />} />
                <Route path="fuel" element={<Fuel />} />
                <Route path="thresholds" element={<Thresholds />} />
                <Route path="reports" element={<Reports />} />
                <Route path="notifications" element={<Notifications />} />
              </Route>
            </Routes>
          </div>
          <PerformanceMonitor />
        </Router>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;