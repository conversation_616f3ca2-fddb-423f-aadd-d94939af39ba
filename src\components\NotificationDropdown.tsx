import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, AlertTriangle, Info, AlertCircle, Check<PERSON>ircle, Setting<PERSON>, Filter } from 'lucide-react';
import { useNotifications } from '../hooks/useNotifications';
import { useNotificationCount } from '../hooks/useNotificationCount';
import { Notification } from '../types';

const NotificationDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<{
    type?: string;
    severity?: string;
    read?: boolean;
  }>({});

  const dropdownRef = useRef<HTMLDivElement>(null);

  // Use enhanced hooks with real-time updates
  const {
    data: notifications,
    loading,
    error,
    lastUpdated,
    markAsRead,
    deleteNotification,
    markA<PERSON><PERSON>Read,
    markMultipleAsRead,
    deleteMultiple
  } = useNotifications({
    realTime: true,
    autoRefresh: true,
    refreshInterval: 30000,
    filters
  });

  const { data: unreadCount } = useNotificationCount({
    realTime: true,
    autoRefresh: true
  });

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Enhanced notification management functions
  const handleMarkAsRead = async (id: string) => {
    await markAsRead(id);
    setSelectedNotifications(prev => prev.filter(nId => nId !== id));
  };

  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
    setSelectedNotifications([]);
  };

  const handleDeleteNotification = async (id: string) => {
    await deleteNotification(id);
    setSelectedNotifications(prev => prev.filter(nId => nId !== id));
  };

  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.length > 0) {
      await markMultipleAsRead(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedNotifications.length > 0) {
      await deleteMultiple(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const toggleNotificationSelection = (id: string) => {
    setSelectedNotifications(prev =>
      prev.includes(id)
        ? prev.filter(nId => nId !== id)
        : [...prev, id]
    );
  };

  const selectAllNotifications = () => {
    const allIds = notifications?.map(n => n.id) || [];
    setSelectedNotifications(allIds);
  };

  const clearSelection = () => {
    setSelectedNotifications([]);
  };

  const applyFilter = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value
    }));
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getSeverityColor = (severity: Notification['severity']) => {
    switch (severity) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50';
      case 'high':
        return 'border-l-orange-500 bg-orange-50';
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-50';
      default:
        return 'border-l-blue-500 bg-blue-50';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const unreadNotifications = notifications?.filter(n => !n.read) || [];
  const hasUnread = unreadNotifications.length > 0;

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
      >
        <Bell className="h-6 w-6" />
        {hasUnread && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {unreadCount && unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="text-gray-400 hover:text-gray-600"
                  title="Filters"
                >
                  <Filter className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="flex flex-wrap gap-2 mb-3 p-2 bg-gray-50 rounded">
                <select
                  value={filters.type || 'all'}
                  onChange={(e) => applyFilter('type', e.target.value)}
                  className="text-xs border rounded px-2 py-1"
                >
                  <option value="all">All Types</option>
                  <option value="info">Info</option>
                  <option value="success">Success</option>
                  <option value="warning">Warning</option>
                  <option value="error">Error</option>
                </select>
                <select
                  value={filters.severity || 'all'}
                  onChange={(e) => applyFilter('severity', e.target.value)}
                  className="text-xs border rounded px-2 py-1"
                >
                  <option value="all">All Priorities</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
                <select
                  value={filters.read === undefined ? 'all' : filters.read ? 'read' : 'unread'}
                  onChange={(e) => applyFilter('read', e.target.value === 'all' ? undefined : e.target.value === 'read')}
                  className="text-xs border rounded px-2 py-1"
                >
                  <option value="all">All Status</option>
                  <option value="unread">Unread</option>
                  <option value="read">Read</option>
                </select>
              </div>
            )}

            {/* Bulk Actions */}
            {selectedNotifications.length > 0 && (
              <div className="flex items-center justify-between p-2 bg-blue-50 rounded mb-2">
                <span className="text-sm text-blue-700">
                  {selectedNotifications.length} selected
                </span>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleBulkMarkAsRead}
                    className="text-xs text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                  >
                    <Check className="h-3 w-3" />
                    <span>Mark Read</span>
                  </button>
                  <button
                    onClick={handleBulkDelete}
                    className="text-xs text-red-600 hover:text-red-800 flex items-center space-x-1"
                  >
                    <X className="h-3 w-3" />
                    <span>Delete</span>
                  </button>
                  <button
                    onClick={clearSelection}
                    className="text-xs text-gray-600 hover:text-gray-800"
                  >
                    Clear
                  </button>
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-3">
                {notifications && notifications.length > 0 && (
                  <button
                    onClick={selectedNotifications.length === notifications.length ? clearSelection : selectAllNotifications}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {selectedNotifications.length === notifications.length ? 'Deselect All' : 'Select All'}
                  </button>
                )}
                {hasUnread && (
                  <button
                    onClick={handleMarkAllAsRead}
                    className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                  >
                    <CheckCheck className="h-4 w-4" />
                    <span>Mark all read</span>
                  </button>
                )}
              </div>
              {lastUpdated && (
                <span className="text-xs text-gray-500">
                  Updated {formatTimeAgo(lastUpdated.toISOString())}
                </span>
              )}
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2">Loading notifications...</p>
              </div>
            ) : notifications && notifications.length > 0 ? (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`border-l-4 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors ${
                    !notification.read ? getSeverityColor(notification.severity) : 'border-l-gray-300 bg-white'
                  } ${selectedNotifications.includes(notification.id) ? 'bg-blue-50' : ''}`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Selection Checkbox */}
                    <input
                      type="checkbox"
                      checked={selectedNotifications.includes(notification.id)}
                      onChange={() => toggleNotificationSelection(notification.id)}
                      className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />

                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <h4 className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-600'}`}>
                          {notification.title}
                          {!notification.read && (
                            <span className="ml-2 inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                          )}
                        </h4>
                        <div className="flex items-center space-x-1 ml-2">
                          {!notification.read && (
                            <button
                              onClick={() => handleMarkAsRead(notification.id)}
                              className="text-blue-600 hover:text-blue-800"
                              title="Mark as read"
                            >
                              <Check className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteNotification(notification.id)}
                            className="text-gray-400 hover:text-red-600"
                            title="Delete notification"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      <p className={`text-sm mt-1 ${!notification.read ? 'text-gray-700' : 'text-gray-500'}`}>
                        {notification.message}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            {formatTimeAgo(notification.created_at)}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            notification.severity === 'urgent' ? 'bg-red-100 text-red-800' :
                            notification.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                            notification.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {notification.severity}
                          </span>
                        </div>
                        {notification.action_url && notification.action_text && (
                          <a
                            href={notification.action_url}
                            className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                            onClick={() => setIsOpen(false)}
                          >
                            {notification.action_text}
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-8 text-center text-gray-500">
                <Bell className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                <p className="text-lg font-medium">No notifications</p>
                <p className="text-sm">You're all caught up!</p>
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications && notifications.length > 0 && (
            <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to notifications page if it exists
                }}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                View all notifications
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
