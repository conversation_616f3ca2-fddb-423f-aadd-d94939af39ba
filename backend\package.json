{"name": "fleet-management-backend", "version": "1.0.0", "description": "Backend API for Fleet Management System using Supabase", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["fleet", "management", "api", "supabase", "nodejs", "express"], "author": "Fleet Management Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}