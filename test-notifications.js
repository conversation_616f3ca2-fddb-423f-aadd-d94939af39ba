/**
 * Test script to verify notifications system functionality
 * Tests both the API functions and real-time updates
 * Renamed to .cjs to work with ES modules
 */

const puppeteer = require('puppeteer');

const TEST_URL = 'http://localhost:5175';
const DEMO_CREDENTIALS = { email: '<EMAIL>', password: 'demo123' };

class NotificationsTest {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async setup() {
    console.log('🚀 Setting up notifications test...');
    this.browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1280, height: 720 }
    });
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Browser Error:', msg.text());
      }
    });
  }

  async login() {
    console.log('🔐 Logging in...');
    await this.page.goto(TEST_URL);
    await this.page.waitForSelector('input[type="email"]', { timeout: 10000 });
    
    await this.page.type('input[type="email"]', DEMO_CREDENTIALS.email);
    await this.page.type('input[type="password"]', DEMO_CREDENTIALS.password);
    await this.page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await this.page.waitForSelector('[data-testid="dashboard"], .dashboard, h1', { timeout: 15000 });
    console.log('✅ Login successful');
  }

  async testNotificationDropdown() {
    console.log('🔔 Testing notification dropdown...');
    
    // Look for notification bell icon
    const bellSelector = 'button[title*="notification"], button:has(svg), .notification-bell, [data-testid="notification-bell"]';
    await this.page.waitForSelector(bellSelector, { timeout: 10000 });
    
    // Click the notification bell
    await this.page.click(bellSelector);
    await this.page.waitForTimeout(2000);
    
    // Check if dropdown opened
    const dropdownVisible = await this.page.evaluate(() => {
      const dropdown = document.querySelector('.notification-dropdown, [role="menu"], .dropdown-menu');
      return dropdown && dropdown.offsetParent !== null;
    });
    
    if (dropdownVisible) {
      console.log('✅ Notification dropdown opened successfully');
    } else {
      console.log('⚠️ Notification dropdown may not be visible');
    }
    
    return dropdownVisible;
  }

  async testNotificationsPage() {
    console.log('📄 Testing notifications page...');
    
    // Navigate to notifications page
    await this.page.goto(`${TEST_URL}/notifications`);
    await this.page.waitForTimeout(3000);
    
    // Check if page loaded
    const pageLoaded = await this.page.evaluate(() => {
      return document.body.innerText.includes('Notifications') || 
             document.querySelector('h1, .page-title') !== null;
    });
    
    if (pageLoaded) {
      console.log('✅ Notifications page loaded successfully');
      
      // Check for key elements
      const elements = await this.page.evaluate(() => {
        return {
          hasCreateButton: document.body.innerText.includes('Create Notification'),
          hasFilters: document.body.innerText.includes('All Types') || document.body.innerText.includes('Filter'),
          hasSearchBox: document.querySelector('input[placeholder*="search"], input[type="search"]') !== null,
          hasNotificationsList: document.querySelector('.notification-item, .notification, [data-testid="notification"]') !== null
        };
      });
      
      console.log('📊 Page elements:', elements);
      return true;
    } else {
      console.log('❌ Notifications page failed to load');
      return false;
    }
  }

  async testCreateNotification() {
    console.log('➕ Testing create notification functionality...');
    
    try {
      // Look for create button
      const createButtonSelector = 'button:has-text("Create Notification"), button[title*="Create"], .create-notification';
      await this.page.waitForSelector('button', { timeout: 5000 });
      
      // Find and click create button
      const createButton = await this.page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => 
          btn.textContent.includes('Create Notification') || 
          btn.textContent.includes('Create') ||
          btn.title?.includes('Create')
        );
      });
      
      if (createButton) {
        await this.page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const btn = buttons.find(btn => 
            btn.textContent.includes('Create Notification') || 
            btn.textContent.includes('Create')
          );
          if (btn) btn.click();
        });
        
        await this.page.waitForTimeout(2000);
        
        // Check if modal opened
        const modalVisible = await this.page.evaluate(() => {
          const modal = document.querySelector('.modal, [role="dialog"], .create-notification-modal');
          return modal && modal.offsetParent !== null;
        });
        
        if (modalVisible) {
          console.log('✅ Create notification modal opened successfully');
          return true;
        } else {
          console.log('⚠️ Create notification modal may not be visible');
          return false;
        }
      } else {
        console.log('⚠️ Create notification button not found');
        return false;
      }
    } catch (error) {
      console.log('❌ Error testing create notification:', error.message);
      return false;
    }
  }

  async testRealTimeUpdates() {
    console.log('⚡ Testing real-time updates...');
    
    // Check if Supabase real-time is working
    const realTimeStatus = await this.page.evaluate(() => {
      return new Promise((resolve) => {
        // Check for Supabase client and subscriptions
        const hasSupabase = window.supabase !== undefined;
        const hasSubscriptions = window.supabaseSubscriptions !== undefined;
        
        setTimeout(() => {
          resolve({
            hasSupabase,
            hasSubscriptions,
            hasWebSocket: window.WebSocket !== undefined
          });
        }, 1000);
      });
    });
    
    console.log('📡 Real-time status:', realTimeStatus);
    return realTimeStatus.hasWebSocket;
  }

  async runAllTests() {
    try {
      await this.setup();
      await this.login();
      
      const results = {
        dropdown: await this.testNotificationDropdown(),
        page: await this.testNotificationsPage(),
        create: await this.testCreateNotification(),
        realTime: await this.testRealTimeUpdates()
      };
      
      console.log('\n📋 Test Results Summary:');
      console.log('========================');
      Object.entries(results).forEach(([test, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
      });
      
      const allPassed = Object.values(results).every(result => result);
      console.log(`\n🎯 Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
      
      return results;
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      return null;
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Cleanup completed');
    }
  }
}

// Run the tests
async function main() {
  const test = new NotificationsTest();
  
  try {
    await test.runAllTests();
  } finally {
    await test.cleanup();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = NotificationsTest;
