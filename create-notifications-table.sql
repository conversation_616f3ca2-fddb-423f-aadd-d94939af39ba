-- Essential SQL to create notifications table
-- Copy and paste this into Supabase SQL Editor

-- 1. Create the notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    severity TEXT NOT NULL DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'urgent')),
    read BOOLEAN NOT NULL DEFAULT false,
    action_url TEXT,
    action_text TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at DESC);

-- 3. Enable Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies
CREATE POLICY "Users can view own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own notifications" ON public.notifications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own notifications" ON public.notifications
    FOR DELETE USING (auth.uid() = user_id);

-- 5. Enable realtime
ALTER PUBLICATION supabase_realtime ADD TABLE public.notifications;

-- 6. Insert sample notifications for demo user (if exists)
INSERT INTO public.notifications (user_id, title, message, type, severity, action_url, action_text)
SELECT 
    au.id,
    'Welcome to Fleet Management System',
    'Your account has been successfully set up. Explore the dashboard to get started.',
    'success',
    'medium',
    '/dashboard',
    'View Dashboard'
FROM auth.users au 
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.notifications n 
    WHERE n.user_id = au.id AND n.title = 'Welcome to Fleet Management System'
);

INSERT INTO public.notifications (user_id, title, message, type, severity, action_url, action_text)
SELECT 
    au.id,
    'Vehicle Maintenance Due',
    'Vehicle ABC-123 is due for scheduled maintenance. Please schedule an appointment.',
    'warning',
    'high',
    '/maintenance',
    'Schedule Maintenance'
FROM auth.users au 
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.notifications n 
    WHERE n.user_id = au.id AND n.title = 'Vehicle Maintenance Due'
);

INSERT INTO public.notifications (user_id, title, message, type, severity, action_url, action_text)
SELECT 
    au.id,
    'License Expiry Alert',
    'Driver John Doe''s license expires in 30 days. Please renew immediately.',
    'warning',
    'urgent',
    '/drivers',
    'View Drivers'
FROM auth.users au 
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.notifications n 
    WHERE n.user_id = au.id AND n.title = 'License Expiry Alert'
);

INSERT INTO public.notifications (user_id, title, message, type, severity)
SELECT 
    au.id,
    'System Update',
    'The Fleet Management System has been updated with new features and improvements.',
    'info',
    'low'
FROM auth.users au 
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.notifications n 
    WHERE n.user_id = au.id AND n.title = 'System Update'
);

INSERT INTO public.notifications (user_id, title, message, type, severity, action_url, action_text)
SELECT 
    au.id,
    'Fuel Report Ready',
    'Your monthly fuel consumption report is now available for download.',
    'info',
    'medium',
    '/reports',
    'View Reports'
FROM auth.users au 
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.notifications n 
    WHERE n.user_id = au.id AND n.title = 'Fuel Report Ready'
);
