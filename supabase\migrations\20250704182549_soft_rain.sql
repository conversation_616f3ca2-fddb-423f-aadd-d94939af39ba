/*
  # تحسين سياسات المصادقة وإصلاح المشاكل

  1. المشاكل المحلولة
    - إزالة التكرار اللانهائي في سياسات RLS
    - تحسين سياسات الوصول للبيانات
    - إضافة سياسات أكثر أماناً ووضوحاً

  2. التحسينات
    - سياسات منفصلة للقراءة والكتابة
    - تجنب الاستعلامات الدائرية
    - استخدام auth.uid() مباشرة حيثما أمكن
*/

-- إزالة جميع السياسات الموجودة للمستخدمين
DROP POLICY IF EXISTS "Users can read own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Admins can read all users" ON users;
DROP POLICY IF EXISTS "Admins can create users" ON users;
DROP POLICY IF EXISTS "Admins can update users" ON users;
DROP POLICY IF EXISTS "Super Admins can delete users" ON users;

-- إنشاء سياسات جديدة محسنة للمستخدمين

-- 1. المستخدمون يمكنهم قراءة بياناتهم الشخصية
CREATE POLICY "users_select_own"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = auth_id);

-- 2. المستخدمون يمكنهم تحديث بياناتهم الشخصية (حقول محددة فقط)
CREATE POLICY "users_update_own"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = auth_id)
  WITH CHECK (
    auth.uid() = auth_id 
    AND role = OLD.role  -- منع تغيير الدور
    AND status = OLD.status  -- منع تغيير الحالة
  );

-- 3. إدراج مستخدمين جدد (للتسجيل)
CREATE POLICY "users_insert_new"
  ON users
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = auth_id);

-- 4. المديرون يمكنهم قراءة جميع المستخدمين
-- استخدام دالة مساعدة لتجنب التكرار
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM users 
    WHERE auth_id = user_id 
    AND role IN ('Super Admin', 'Admin')
    LIMIT 1
  );
$$;

CREATE POLICY "admins_select_all_users"
  ON users
  FOR SELECT
  TO authenticated
  USING (is_admin(auth.uid()));

-- 5. المديرون يمكنهم إدراج مستخدمين جدد
CREATE POLICY "admins_insert_users"
  ON users
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin(auth.uid()));

-- 6. المديرون يمكنهم تحديث المستخدمين
CREATE POLICY "admins_update_users"
  ON users
  FOR UPDATE
  TO authenticated
  USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

-- 7. المديرون العامون فقط يمكنهم حذف المستخدمين
CREATE POLICY "super_admins_delete_users"
  ON users
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE auth_id = auth.uid() 
      AND role = 'Super Admin'
      LIMIT 1
    )
  );

-- تحسين سياسات الفروع
DROP POLICY IF EXISTS "Users can read branches" ON branches;
DROP POLICY IF EXISTS "Admins can manage branches" ON branches;

CREATE POLICY "branches_select_all"
  ON branches
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "admins_manage_branches"
  ON branches
  FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

-- تحسين سياسات السائقين
DROP POLICY IF EXISTS "Users can read drivers in their branch" ON drivers;
DROP POLICY IF EXISTS "Managers can manage drivers" ON drivers;

-- دالة للتحقق من صلاحيات إدارة السائقين
CREATE OR REPLACE FUNCTION can_manage_drivers(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM users 
    WHERE auth_id = user_id 
    AND role IN ('Super Admin', 'Admin', 'Manager')
    LIMIT 1
  );
$$;

CREATE POLICY "drivers_select_by_branch"
  ON drivers
  FOR SELECT
  TO authenticated
  USING (
    is_admin(auth.uid()) OR
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.auth_id = auth.uid()
      AND (u.branch_id = drivers.branch_id OR u.role IN ('Super Admin', 'Admin'))
    )
  );

CREATE POLICY "managers_manage_drivers"
  ON drivers
  FOR ALL
  TO authenticated
  USING (can_manage_drivers(auth.uid()))
  WITH CHECK (can_manage_drivers(auth.uid()));

-- تحسين سياسات المركبات
DROP POLICY IF EXISTS "Users can read vehicles in their branch" ON vehicles;
DROP POLICY IF EXISTS "Managers can manage vehicles" ON vehicles;

CREATE POLICY "vehicles_select_by_branch"
  ON vehicles
  FOR SELECT
  TO authenticated
  USING (
    is_admin(auth.uid()) OR
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.auth_id = auth.uid()
      AND (u.branch_id = vehicles.branch_id OR u.role IN ('Super Admin', 'Admin'))
    )
  );

CREATE POLICY "managers_manage_vehicles"
  ON vehicles
  FOR ALL
  TO authenticated
  USING (can_manage_drivers(auth.uid()))
  WITH CHECK (can_manage_drivers(auth.uid()));

-- تحسين سياسات الصيانة
DROP POLICY IF EXISTS "Users can read maintenance records" ON maintenance;
DROP POLICY IF EXISTS "Managers can manage maintenance" ON maintenance;

CREATE POLICY "maintenance_select_by_access"
  ON maintenance
  FOR SELECT
  TO authenticated
  USING (
    is_admin(auth.uid()) OR
    EXISTS (
      SELECT 1 FROM users u
      JOIN vehicles v ON v.branch_id = u.branch_id
      WHERE u.auth_id = auth.uid()
      AND v.id = maintenance.vehicle_id
    )
  );

CREATE POLICY "managers_manage_maintenance"
  ON maintenance
  FOR ALL
  TO authenticated
  USING (can_manage_drivers(auth.uid()))
  WITH CHECK (can_manage_drivers(auth.uid()));

-- تحسين سياسات الوقود
DROP POLICY IF EXISTS "Users can read fuel records" ON fuel;
DROP POLICY IF EXISTS "Drivers can add fuel records" ON fuel;
DROP POLICY IF EXISTS "Managers can manage fuel records" ON fuel;

CREATE POLICY "fuel_select_by_access"
  ON fuel
  FOR SELECT
  TO authenticated
  USING (
    is_admin(auth.uid()) OR
    EXISTS (
      SELECT 1 FROM users u
      JOIN vehicles v ON v.branch_id = u.branch_id
      WHERE u.auth_id = auth.uid()
      AND v.id = fuel.vehicle_id
    )
  );

CREATE POLICY "authenticated_users_insert_fuel"
  ON fuel
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "managers_manage_fuel"
  ON fuel
  FOR ALL
  TO authenticated
  USING (can_manage_drivers(auth.uid()))
  WITH CHECK (can_manage_drivers(auth.uid()));

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_users_auth_id ON users(auth_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_branch_role ON users(branch_id, role);

-- تحديث الطوابع الزمنية تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إضافة المشغلات للجداول
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_branches_updated_at ON branches;
CREATE TRIGGER update_branches_updated_at
    BEFORE UPDATE ON branches
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_drivers_updated_at ON drivers;
CREATE TRIGGER update_drivers_updated_at
    BEFORE UPDATE ON drivers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_vehicles_updated_at ON vehicles;
CREATE TRIGGER update_vehicles_updated_at
    BEFORE UPDATE ON vehicles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_maintenance_updated_at ON maintenance;
CREATE TRIGGER update_maintenance_updated_at
    BEFORE UPDATE ON maintenance
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_fuel_updated_at ON fuel;
CREATE TRIGGER update_fuel_updated_at
    BEFORE UPDATE ON fuel
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_thresholds_updated_at ON thresholds;
CREATE TRIGGER update_thresholds_updated_at
    BEFORE UPDATE ON thresholds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();