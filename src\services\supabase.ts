import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Missing Supabase environment variables. Using demo mode.');
}

export const supabase = supabaseUrl && supabaseAnonKey 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null;

// Database types
export interface User {
  id: string;
  auth_id?: string;
  name: string;
  email: string;
  role: 'Super Admin' | 'Admin' | 'Manager' | 'Driver';
  status: 'Active' | 'Inactive';
  manager_id?: string;
  branch_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Branch {
  id: string;
  name: string;
  location: string;
  address?: string;
  phone?: string;
  email?: string;
  manager_id?: string;
  status: 'Active' | 'Inactive';
  created_at: string;
  updated_at: string;
}

export interface Driver {
  id: string;
  code: number;
  name_en: string;
  name_ar: string;
  work_number?: string;
  personal_number?: string;
  user_profile_url?: string;
  username?: string;
  license_number?: string;
  license_expiry?: string;
  phone?: string;
  email?: string;
  hire_date?: string;
  branch_id?: string;
  manager_id?: string;
  user_id?: string;
  status: 'Active' | 'Inactive' | 'Suspended';
  created_at: string;
  updated_at: string;
}

export interface Vehicle {
  id: string;
  vehicle_id: string;
  license_plate: string;
  service_type?: string;
  vehicle_type?: string;
  make?: string;
  model?: string;
  year?: number;
  color?: string;
  vin_number?: string;
  fuel_type: string;
  current_km: number;
  vehicle_status: 'Active' | 'Inactive' | 'Maintenance' | 'Out of Service';
  branch_id?: string;
  current_location?: string;
  driver_id?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Maintenance {
  id: string;
  vehicle_id: string;
  license_plate?: string;
  service_type: string;
  service_center?: string;
  description?: string;
  odometer_reading?: number;
  parts_cost: number;
  labor_cost: number;
  total_cost: number;
  scheduled_date?: string;
  service_date?: string;
  next_service_date?: string;
  completed_date?: string;
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled' | 'Overdue';
  mechanic?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Fuel {
  id: string;
  vehicle_id: string;
  license_plate?: string;
  vehicle_type?: string;
  vehicle_status?: string;
  vin?: string;
  datetime: string;
  driver_id?: string;
  driver_name_en?: string;
  driver_name_ar?: string;
  fuel_type: string;
  amount: number;
  quantity: number;
  cost_per_liter?: number;
  pump_image_reading?: number;
  difference?: number;
  odometer?: number;
  distance?: number;
  consumption_rate?: number;
  cost_per_meter?: number;
  branch_id?: string;
  vehicle_group?: string;
  vehicle_model?: number;
  station?: string;
  created_at: string;
  updated_at: string;
}

export interface Threshold {
  id: string;
  code: string;
  check_type: 'Maintenance' | 'Tier' | 'Fuel' | 'Driver';
  car_type?: string;
  parameter: string;
  condition_type: 'greater_than' | 'less_than' | 'days_since' | 'days_until';
  value: number;
  unit: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  severity: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  action_url?: string;
  action_text?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
  expires_at?: string;
}

// API functions
export const supabaseAPI = {
  // Authentication
  auth: {
    signUp: async (email: string, password: string, userData: { name: string; role: string }) => {
      if (!supabase) {
        throw new Error('Supabase not configured');
      }

      try {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              name: userData.name,
              role: userData.role
            }
          }
        });

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('Sign up error:', error);
        return { data: null, error };
      }
    },

    signIn: async (email: string, password: string) => {
      if (!supabase) {
        throw new Error('Supabase not configured');
      }

      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password
        });

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('Sign in error:', error);
        return { data: null, error };
      }
    },

    signOut: async () => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase.auth.signOut();
        return { error };
      } catch (error: any) {
        console.error('Sign out error:', error);
        return { error };
      }
    },

    getUser: async () => {
      if (!supabase) {
        throw new Error('Supabase not configured');
      }

      try {
        const { data, error } = await supabase.auth.getUser();
        return { data, error };
      } catch (error: any) {
        console.error('Get user error:', error);
        return { data: null, error };
      }
    },

    getSession: async () => {
      if (!supabase) {
        return { data: { session: null }, error: null };
      }

      try {
        const { data, error } = await supabase.auth.getSession();
        return { data, error };
      } catch (error: any) {
        console.error('Get session error:', error);
        return { data: { session: null }, error };
      }
    },

    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      if (!supabase) {
        return { data: { subscription: { unsubscribe: () => {} } } };
      }

      return supabase.auth.onAuthStateChange(callback);
    }
  },

  // Users
  users: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get users error:', error);
        return { data: [], error };
      }
    },

    getById: async (id: string) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('auth_id', id)
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Get user by ID error:', error);
        return { data: null, error };
      }
    },

    create: async (user: Partial<User>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .insert(user)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create user error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, user: Partial<User>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .update({ ...user, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update user error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase
          .from('users')
          .delete()
          .eq('id', id);
        return { error };
      } catch (error: any) {
        console.error('Delete user error:', error);
        return { error };
      }
    }
  },

  // Branches
  branches: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('branches')
          .select(`
            *
          `)
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get branches error:', error);
        return { data: [], error };
      }
    },

    create: async (branch: Partial<Branch>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('branches')
          .insert(branch)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create branch error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, branch: Partial<Branch>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('branches')
          .update({ ...branch, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update branch error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase
          .from('branches')
          .delete()
          .eq('id', id);
        return { error };
      } catch (error: any) {
        console.error('Delete branch error:', error);
        return { error };
      }
    }
  },

  // Drivers
  drivers: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('drivers')
          .select(`
            *,
            branch:branches(name)
          `)
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get drivers error:', error);
        return { data: [], error };
      }
    },

    create: async (driver: Partial<Driver>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('drivers')
          .insert(driver)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create driver error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, driver: Partial<Driver>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('drivers')
          .update({ ...driver, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update driver error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase
          .from('drivers')
          .delete()
          .eq('id', id);
        return { error };
      } catch (error: any) {
        console.error('Delete driver error:', error);
        return { error };
      }
    }
  },

  // Vehicles
  vehicles: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('vehicles')
          .select(`
            *,
            branch:branches(name),
            driver:drivers(name_en, name_ar)
          `)
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get vehicles error:', error);
        return { data: [], error };
      }
    },

    create: async (vehicle: Partial<Vehicle>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('vehicles')
          .insert(vehicle)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create vehicle error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, vehicle: Partial<Vehicle>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('vehicles')
          .update({ ...vehicle, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update vehicle error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase
          .from('vehicles')
          .delete()
          .eq('id', id);
        return { error };
      } catch (error: any) {
        console.error('Delete vehicle error:', error);
        return { error };
      }
    }
  },

  // Maintenance
  maintenance: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('maintenance')
          .select(`
            *,
            vehicle:vehicles(license_plate, make, model)
          `)
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get maintenance error:', error);
        return { data: [], error };
      }
    },

    create: async (maintenance: Partial<Maintenance>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('maintenance')
          .insert(maintenance)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create maintenance error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, maintenance: Partial<Maintenance>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('maintenance')
          .update({ ...maintenance, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update maintenance error:', error);
        return { data: null, error };
      }
    }
  },

  // Fuel
  fuel: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('fuel')
          .select(`
            *,
            vehicle:vehicles(license_plate, make, model),
            driver:drivers(name_en, name_ar),
            branch:branches(name)
          `)
          .order('datetime', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get fuel error:', error);
        return { data: [], error };
      }
    },

    create: async (fuel: Partial<Fuel>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('fuel')
          .insert(fuel)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create fuel error:', error);
        return { data: null, error };
      }
    }
  },

  // Thresholds
  thresholds: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('thresholds')
          .select('*')
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get thresholds error:', error);
        return { data: [], error };
      }
    },

    create: async (threshold: Partial<Threshold>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('thresholds')
          .insert(threshold)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create threshold error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, threshold: Partial<Threshold>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('thresholds')
          .update({ ...threshold, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update threshold error:', error);
        return { data: null, error };
      }
    }
  },

  // Dashboard stats
  dashboard: {
    getStats: async () => {
      if (!supabase) {
        // Return demo data when Supabase is not configured
        console.log('📊 Dashboard: Using demo data (Supabase not configured)');
        return {
          data: {
            totalVehicles: 125,
            activeVehicles: 98,
            totalDrivers: 87,
            activeDrivers: 65,
            totalBranches: 8,
            pendingMaintenance: 12,
            fuelConsumption: 2450,
            alerts: 5,
            maintenanceInProgress: 3,
            vehiclesInMaintenance: 15,
            expiredLicenses: 2,
            lowFuelAlerts: 8
          },
          error: null
        };
      }

      try {
        console.log('📊 Dashboard: Fetching real-time statistics...');

        // Get current date for calculations
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        const sevenDaysFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

        // Fetch all required data in parallel for better performance
        const [vehicles, drivers, branches, maintenance, fuel, thresholds] = await Promise.all([
          supabase.from('vehicles').select('vehicle_status, fuel_type, current_km'),
          supabase.from('drivers').select('status, license_expiry'),
          supabase.from('branches').select('id'),
          supabase.from('maintenance').select('status, scheduled_date, next_service_date'),
          supabase.from('fuel').select('quantity, datetime').gte('datetime', thirtyDaysAgo.toISOString()),
          supabase.from('thresholds').select('*')
        ]);

        // Calculate basic counts
        const totalVehicles = vehicles.data?.length || 0;
        const activeVehicles = vehicles.data?.filter(v => v.vehicle_status === 'Active').length || 0;
        const vehiclesInMaintenance = vehicles.data?.filter(v => v.vehicle_status === 'Maintenance').length || 0;

        const totalDrivers = drivers.data?.length || 0;
        const activeDrivers = drivers.data?.filter(d => d.status === 'Active').length || 0;

        const totalBranches = branches.data?.length || 0;

        // Calculate maintenance metrics
        const pendingMaintenance = maintenance.data?.filter(m =>
          m.status === 'Scheduled' || m.status === 'In Progress'
        ).length || 0;

        const maintenanceInProgress = maintenance.data?.filter(m =>
          m.status === 'In Progress'
        ).length || 0;

        // Calculate overdue maintenance
        const overdueMaintenance = maintenance.data?.filter(m => {
          if (!m.next_service_date) return false;
          const nextService = new Date(m.next_service_date);
          return nextService <= today && m.status !== 'Completed';
        }).length || 0;

        // Calculate fuel consumption for last 30 days
        const fuelConsumption = Math.round(
          fuel.data?.reduce((sum, f) => sum + (f.quantity || 0), 0) || 0
        );

        // Calculate alerts
        let alerts = 0;

        // License expiry alerts (within 30 days)
        const expiredLicenses = drivers.data?.filter(d => {
          if (!d.license_expiry) return false;
          const expiryDate = new Date(d.license_expiry);
          return expiryDate <= sevenDaysFromNow;
        }).length || 0;
        alerts += expiredLicenses;

        // Add overdue maintenance to alerts
        alerts += overdueMaintenance;

        // Add vehicles in maintenance as alerts
        alerts += vehiclesInMaintenance;

        // Calculate low fuel alerts (placeholder - would need fuel level data)
        const lowFuelAlerts = Math.floor(totalVehicles * 0.1); // Assume 10% of vehicles have low fuel
        alerts += lowFuelAlerts;

        const stats = {
          totalVehicles,
          activeVehicles,
          totalDrivers,
          activeDrivers,
          totalBranches,
          pendingMaintenance,
          fuelConsumption,
          alerts,
          maintenanceInProgress,
          vehiclesInMaintenance,
          expiredLicenses,
          lowFuelAlerts,
          overdueMaintenance
        };

        console.log('✅ Dashboard: Statistics calculated successfully', stats);
        return { data: stats, error: null };
      } catch (error: any) {
        console.error('❌ Dashboard: Error fetching statistics:', error);
        // Return demo data on error to prevent UI breaking
        return {
          data: {
            totalVehicles: 125,
            activeVehicles: 98,
            totalDrivers: 87,
            activeDrivers: 65,
            totalBranches: 8,
            pendingMaintenance: 12,
            fuelConsumption: 2450,
            alerts: 5,
            maintenanceInProgress: 3,
            vehiclesInMaintenance: 15,
            expiredLicenses: 2,
            lowFuelAlerts: 8
          },
          error
        };
      }
    },

    // Get detailed alerts for notifications
    getAlerts: async () => {
      if (!supabase) {
        return {
          data: [
            {
              id: '1',
              type: 'maintenance',
              title: 'Vehicle Maintenance Due',
              message: 'Vehicle ABC-123 requires scheduled maintenance',
              severity: 'warning',
              created_at: new Date().toISOString(),
              read: false
            },
            {
              id: '2',
              type: 'license',
              title: 'Driver License Expiring',
              message: 'John Doe\'s license expires in 5 days',
              severity: 'urgent',
              created_at: new Date().toISOString(),
              read: false
            }
          ],
          error: null
        };
      }

      try {
        const today = new Date();
        const sevenDaysFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

        const [vehicles, drivers, maintenance] = await Promise.all([
          supabase.from('vehicles').select('id, license_plate, vehicle_status'),
          supabase.from('drivers').select('id, name_en, license_expiry'),
          supabase.from('maintenance').select('id, vehicle_id, next_service_date, status').eq('status', 'Scheduled')
        ]);

        const alerts = [];

        // License expiry alerts
        drivers.data?.forEach(driver => {
          if (driver.license_expiry) {
            const expiryDate = new Date(driver.license_expiry);
            if (expiryDate <= sevenDaysFromNow) {
              const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
              alerts.push({
                id: `license-${driver.id}`,
                type: 'license',
                title: 'Driver License Expiring',
                message: `${driver.name_en}'s license expires in ${daysUntilExpiry} days`,
                severity: daysUntilExpiry <= 3 ? 'urgent' : 'warning',
                created_at: new Date().toISOString(),
                read: false
              });
            }
          }
        });

        // Maintenance alerts
        maintenance.data?.forEach(maint => {
          if (maint.next_service_date) {
            const serviceDate = new Date(maint.next_service_date);
            if (serviceDate <= today) {
              const vehicle = vehicles.data?.find(v => v.id === maint.vehicle_id);
              alerts.push({
                id: `maintenance-${maint.id}`,
                type: 'maintenance',
                title: 'Vehicle Maintenance Overdue',
                message: `Vehicle ${vehicle?.license_plate || 'Unknown'} has overdue maintenance`,
                severity: 'urgent',
                created_at: new Date().toISOString(),
                read: false
              });
            }
          }
        });

        // Vehicle status alerts
        vehicles.data?.forEach(vehicle => {
          if (vehicle.vehicle_status === 'Maintenance') {
            alerts.push({
              id: `vehicle-${vehicle.id}`,
              type: 'vehicle',
              title: 'Vehicle in Maintenance',
              message: `Vehicle ${vehicle.license_plate} is currently in maintenance`,
              severity: 'info',
              created_at: new Date().toISOString(),
              read: false
            });
          }
        });

        return { data: alerts, error: null };
      } catch (error: any) {
        console.error('Get alerts error:', error);
        return { data: [], error };
      }
    }
  },

  // Notifications
  notifications: {
    getAll: async () => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured, using demo data');
        return {
          data: [
            {
              id: 'demo-1',
              user_id: 'demo-user',
              title: 'Welcome to Fleet Management',
              message: 'Your account has been successfully created!',
              type: 'success' as const,
              severity: 'low' as const,
              read: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ],
          error: null
        };
      }

      try {
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) {
          return { data: [], error: new Error('User not authenticated') };
        }

        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', user.user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;
        return { data: data || [], error: null };
      } catch (error: any) {
        console.error('🔔 Get notifications error:', error);
        return { data: [], error };
      }
    },

    markAsRead: async (id: string) => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured');
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('notifications')
          .update({ read: true, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('🔔 Mark notification as read error:', error);
        return { data: null, error };
      }
    },

    markAllAsRead: async () => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured');
        return { data: null, error: null };
      }

      try {
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) {
          return { data: null, error: new Error('User not authenticated') };
        }

        const { data, error } = await supabase
          .from('notifications')
          .update({ read: true, updated_at: new Date().toISOString() })
          .eq('user_id', user.user.id)
          .eq('read', false);

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('🔔 Mark all notifications as read error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured');
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('notifications')
          .delete()
          .eq('id', id);

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('🔔 Delete notification error:', error);
        return { data: null, error };
      }
    },

    getUnreadCount: async () => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured');
        return { data: 1, error: null };
      }

      try {
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) {
          return { data: 0, error: new Error('User not authenticated') };
        }

        const { count, error } = await supabase
          .from('notifications')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.user.id)
          .eq('read', false);

        if (error) throw error;
        return { data: count || 0, error: null };
      } catch (error: any) {
        console.error('🔔 Get unread count error:', error);
        return { data: 0, error };
      }
    },

    // Create a new notification
    create: async (notification: Omit<Notification, 'id' | 'created_at' | 'updated_at'>) => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured');
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('notifications')
          .insert({
            ...notification,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) throw error;
        console.log('🔔 Notification created:', data);
        return { data, error: null };
      } catch (error: any) {
        console.error('🔔 Create notification error:', error);
        return { data: null, error };
      }
    },

    // Bulk operations
    markMultipleAsRead: async (ids: string[]) => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured');
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('notifications')
          .update({ read: true, updated_at: new Date().toISOString() })
          .in('id', ids);

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('🔔 Mark multiple notifications as read error:', error);
        return { data: null, error };
      }
    },

    deleteMultiple: async (ids: string[]) => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured');
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('notifications')
          .delete()
          .in('id', ids);

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('🔔 Delete multiple notifications error:', error);
        return { data: null, error };
      }
    },

    // Get notifications with pagination
    getPaginated: async (page: number = 1, limit: number = 20, filters?: {
      type?: string;
      severity?: string;
      read?: boolean;
    }) => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured, using demo data');
        return {
          data: [],
          error: null,
          count: 0,
          hasMore: false
        };
      }

      try {
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) {
          return { data: [], error: new Error('User not authenticated'), count: 0, hasMore: false };
        }

        let query = supabase
          .from('notifications')
          .select('*', { count: 'exact' })
          .eq('user_id', user.user.id);

        // Apply filters
        if (filters?.type) {
          query = query.eq('type', filters.type);
        }
        if (filters?.severity) {
          query = query.eq('severity', filters.severity);
        }
        if (filters?.read !== undefined) {
          query = query.eq('read', filters.read);
        }

        // Apply pagination
        const from = (page - 1) * limit;
        const to = from + limit - 1;

        const { data, error, count } = await query
          .order('created_at', { ascending: false })
          .range(from, to);

        if (error) throw error;

        const hasMore = count ? (from + limit) < count : false;

        return {
          data: data || [],
          error: null,
          count: count || 0,
          hasMore
        };
      } catch (error: any) {
        console.error('🔔 Get paginated notifications error:', error);
        return { data: [], error, count: 0, hasMore: false };
      }
    },

    // Real-time subscription
    subscribe: (callback: (payload: any) => void) => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured, real-time updates disabled');
        return null;
      }

      console.log('🔔 Setting up real-time notifications subscription');

      const subscription = supabase
        .channel('notifications')
        .on('postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'notifications'
          },
          (payload) => {
            console.log('🔔 Real-time notification update:', payload);
            callback(payload);
          }
        )
        .subscribe();

      return subscription;
    },

    // Cleanup expired notifications
    cleanupExpired: async () => {
      if (!supabase) {
        console.warn('🔔 Notifications: Supabase not configured');
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('notifications')
          .delete()
          .lt('expires_at', new Date().toISOString());

        if (error) throw error;
        console.log('🔔 Cleaned up expired notifications:', data);
        return { data, error: null };
      } catch (error: any) {
        console.error('🔔 Cleanup expired notifications error:', error);
        return { data: null, error };
      }
    }
  }
};