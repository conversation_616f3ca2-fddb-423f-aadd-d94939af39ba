import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Missing Supabase environment variables. Using demo mode.');
}

export const supabase = supabaseUrl && supabaseAnonKey 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null;

// Database types
export interface User {
  id: string;
  auth_id?: string;
  name: string;
  email: string;
  role: 'Super Admin' | 'Admin' | 'Manager' | 'Driver';
  status: 'Active' | 'Inactive';
  manager_id?: string;
  branch_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Branch {
  id: string;
  name: string;
  location: string;
  address?: string;
  phone?: string;
  email?: string;
  manager_id?: string;
  status: 'Active' | 'Inactive';
  created_at: string;
  updated_at: string;
}

export interface Driver {
  id: string;
  code: number;
  name_en: string;
  name_ar: string;
  work_number?: string;
  personal_number?: string;
  user_profile_url?: string;
  username?: string;
  license_number?: string;
  license_expiry?: string;
  phone?: string;
  email?: string;
  hire_date?: string;
  branch_id?: string;
  manager_id?: string;
  user_id?: string;
  status: 'Active' | 'Inactive' | 'Suspended';
  created_at: string;
  updated_at: string;
}

export interface Vehicle {
  id: string;
  vehicle_id: string;
  license_plate: string;
  service_type?: string;
  vehicle_type?: string;
  make?: string;
  model?: string;
  year?: number;
  color?: string;
  vin_number?: string;
  fuel_type: string;
  current_km: number;
  vehicle_status: 'Active' | 'Inactive' | 'Maintenance' | 'Out of Service';
  branch_id?: string;
  current_location?: string;
  driver_id?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Maintenance {
  id: string;
  vehicle_id: string;
  license_plate?: string;
  service_type: string;
  service_center?: string;
  description?: string;
  odometer_reading?: number;
  parts_cost: number;
  labor_cost: number;
  total_cost: number;
  scheduled_date?: string;
  service_date?: string;
  next_service_date?: string;
  completed_date?: string;
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled' | 'Overdue';
  mechanic?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Fuel {
  id: string;
  vehicle_id: string;
  license_plate?: string;
  vehicle_type?: string;
  vehicle_status?: string;
  vin?: string;
  datetime: string;
  driver_id?: string;
  driver_name_en?: string;
  driver_name_ar?: string;
  fuel_type: string;
  amount: number;
  quantity: number;
  cost_per_liter?: number;
  pump_image_reading?: number;
  difference?: number;
  odometer?: number;
  distance?: number;
  consumption_rate?: number;
  cost_per_meter?: number;
  branch_id?: string;
  vehicle_group?: string;
  vehicle_model?: number;
  station?: string;
  created_at: string;
  updated_at: string;
}

export interface Threshold {
  id: string;
  code: string;
  check_type: 'Maintenance' | 'Tier' | 'Fuel' | 'Driver';
  car_type?: string;
  parameter: string;
  condition_type: 'greater_than' | 'less_than' | 'days_since' | 'days_until';
  value: number;
  unit: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// API functions
export const supabaseAPI = {
  // Authentication
  auth: {
    signUp: async (email: string, password: string, userData: { name: string; role: string }) => {
      if (!supabase) {
        throw new Error('Supabase not configured');
      }

      try {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              name: userData.name,
              role: userData.role
            }
          }
        });

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('Sign up error:', error);
        return { data: null, error };
      }
    },

    signIn: async (email: string, password: string) => {
      if (!supabase) {
        throw new Error('Supabase not configured');
      }

      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password
        });

        if (error) throw error;
        return { data, error: null };
      } catch (error: any) {
        console.error('Sign in error:', error);
        return { data: null, error };
      }
    },

    signOut: async () => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase.auth.signOut();
        return { error };
      } catch (error: any) {
        console.error('Sign out error:', error);
        return { error };
      }
    },

    getUser: async () => {
      if (!supabase) {
        throw new Error('Supabase not configured');
      }

      try {
        const { data, error } = await supabase.auth.getUser();
        return { data, error };
      } catch (error: any) {
        console.error('Get user error:', error);
        return { data: null, error };
      }
    },

    getSession: async () => {
      if (!supabase) {
        return { data: { session: null }, error: null };
      }

      try {
        const { data, error } = await supabase.auth.getSession();
        return { data, error };
      } catch (error: any) {
        console.error('Get session error:', error);
        return { data: { session: null }, error };
      }
    },

    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      if (!supabase) {
        return { data: { subscription: { unsubscribe: () => {} } } };
      }

      return supabase.auth.onAuthStateChange(callback);
    }
  },

  // Users
  users: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get users error:', error);
        return { data: [], error };
      }
    },

    getById: async (id: string) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('auth_id', id)
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Get user by ID error:', error);
        return { data: null, error };
      }
    },

    create: async (user: Partial<User>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .insert(user)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create user error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, user: Partial<User>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .update({ ...user, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update user error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase
          .from('users')
          .delete()
          .eq('id', id);
        return { error };
      } catch (error: any) {
        console.error('Delete user error:', error);
        return { error };
      }
    }
  },

  // Branches
  branches: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('branches')
          .select(`
            *
          `)
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get branches error:', error);
        return { data: [], error };
      }
    },

    create: async (branch: Partial<Branch>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('branches')
          .insert(branch)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create branch error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, branch: Partial<Branch>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('branches')
          .update({ ...branch, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update branch error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase
          .from('branches')
          .delete()
          .eq('id', id);
        return { error };
      } catch (error: any) {
        console.error('Delete branch error:', error);
        return { error };
      }
    }
  },

  // Drivers
  drivers: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('drivers')
          .select(`
            *,
            branch:branches(name)
          `)
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get drivers error:', error);
        return { data: [], error };
      }
    },

    create: async (driver: Partial<Driver>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('drivers')
          .insert(driver)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create driver error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, driver: Partial<Driver>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('drivers')
          .update({ ...driver, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update driver error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase
          .from('drivers')
          .delete()
          .eq('id', id);
        return { error };
      } catch (error: any) {
        console.error('Delete driver error:', error);
        return { error };
      }
    }
  },

  // Vehicles
  vehicles: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('vehicles')
          .select(`
            *,
            branch:branches(name),
            driver:drivers(name_en, name_ar)
          `)
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get vehicles error:', error);
        return { data: [], error };
      }
    },

    create: async (vehicle: Partial<Vehicle>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('vehicles')
          .insert(vehicle)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create vehicle error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, vehicle: Partial<Vehicle>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('vehicles')
          .update({ ...vehicle, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update vehicle error:', error);
        return { data: null, error };
      }
    },

    delete: async (id: string) => {
      if (!supabase) {
        return { error: null };
      }

      try {
        const { error } = await supabase
          .from('vehicles')
          .delete()
          .eq('id', id);
        return { error };
      } catch (error: any) {
        console.error('Delete vehicle error:', error);
        return { error };
      }
    }
  },

  // Maintenance
  maintenance: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('maintenance')
          .select(`
            *,
            vehicle:vehicles(license_plate, make, model)
          `)
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get maintenance error:', error);
        return { data: [], error };
      }
    },

    create: async (maintenance: Partial<Maintenance>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('maintenance')
          .insert(maintenance)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create maintenance error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, maintenance: Partial<Maintenance>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('maintenance')
          .update({ ...maintenance, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update maintenance error:', error);
        return { data: null, error };
      }
    }
  },

  // Fuel
  fuel: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('fuel')
          .select(`
            *,
            vehicle:vehicles(license_plate, make, model),
            driver:drivers(name_en, name_ar),
            branch:branches(name)
          `)
          .order('datetime', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get fuel error:', error);
        return { data: [], error };
      }
    },

    create: async (fuel: Partial<Fuel>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('fuel')
          .insert(fuel)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create fuel error:', error);
        return { data: null, error };
      }
    }
  },

  // Thresholds
  thresholds: {
    getAll: async () => {
      if (!supabase) {
        return { data: [], error: null };
      }

      try {
        const { data, error } = await supabase
          .from('thresholds')
          .select('*')
          .order('created_at', { ascending: false });
        return { data, error };
      } catch (error: any) {
        console.error('Get thresholds error:', error);
        return { data: [], error };
      }
    },

    create: async (threshold: Partial<Threshold>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('thresholds')
          .insert(threshold)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Create threshold error:', error);
        return { data: null, error };
      }
    },

    update: async (id: string, threshold: Partial<Threshold>) => {
      if (!supabase) {
        return { data: null, error: null };
      }

      try {
        const { data, error } = await supabase
          .from('thresholds')
          .update({ ...threshold, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      } catch (error: any) {
        console.error('Update threshold error:', error);
        return { data: null, error };
      }
    }
  },

  // Dashboard stats
  dashboard: {
    getStats: async () => {
      if (!supabase) {
        // Return mock data when Supabase is not configured
        return {
          data: {
            totalVehicles: 125,
            activeVehicles: 98,
            totalDrivers: 87,
            activeDrivers: 65,
            totalBranches: 8,
            pendingMaintenance: 12,
            fuelConsumption: 2450,
            alerts: 5
          },
          error: null
        };
      }

      try {
        const [vehicles, drivers, branches, maintenance, fuel] = await Promise.all([
          supabase.from('vehicles').select('vehicle_status'),
          supabase.from('drivers').select('status'),
          supabase.from('branches').select('id'),
          supabase.from('maintenance').select('status'),
          supabase.from('fuel').select('quantity').gte('datetime', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        ]);

        const stats = {
          totalVehicles: vehicles.data?.length || 0,
          activeVehicles: vehicles.data?.filter(v => v.vehicle_status === 'Active').length || 0,
          totalDrivers: drivers.data?.length || 0,
          activeDrivers: drivers.data?.filter(d => d.status === 'Active').length || 0,
          totalBranches: branches.data?.length || 0,
          pendingMaintenance: maintenance.data?.filter(m => m.status === 'Scheduled' || m.status === 'In Progress').length || 0,
          fuelConsumption: fuel.data?.reduce((sum, f) => sum + (f.quantity || 0), 0) || 0,
          alerts: 5 // This would be calculated based on thresholds
        };

        return { data: stats, error: null };
      } catch (error: any) {
        console.error('Get dashboard stats error:', error);
        // Return mock data on error
        return {
          data: {
            totalVehicles: 125,
            activeVehicles: 98,
            totalDrivers: 87,
            activeDrivers: 65,
            totalBranches: 8,
            pendingMaintenance: 12,
            fuelConsumption: 2450,
            alerts: 5
          },
          error
        };
      }
    }
  }
};