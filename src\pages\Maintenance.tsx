import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Calendar, Wrench, Car, AlertTriangle } from 'lucide-react';
import { maintenanceAPI } from '../services/api';

interface MaintenanceRecord {
  id: string;
  vehicle_id: string;
  vehicle_plate?: string;
  type: string;
  description: string;
  cost: number;
  scheduled_date: string;
  completed_date?: string;
  status: string;
  mechanic?: string;
  next_service?: string;
}

interface MaintenanceFormData {
  vehicle_id: string;
  vehicle_plate: string;
  type: string;
  description: string;
  cost: number;
  scheduled_date: string;
  status: string;
  mechanic: string;
}

const Maintenance: React.FC = () => {
  const [records, setRecords] = useState<MaintenanceRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [editingRecord, setEditingRecord] = useState<MaintenanceRecord | null>(null);
  const [formData, setFormData] = useState<MaintenanceFormData>({
    vehicle_id: '',
    vehicle_plate: 'VH001',
    type: 'Oil Change',
    description: '',
    cost: 0,
    scheduled_date: new Date().toISOString().split('T')[0],
    status: 'Scheduled',
    mechanic: ''
  });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchMaintenanceRecords();
  }, []);

  const fetchMaintenanceRecords = async () => {
    try {
      const data = await maintenanceAPI.getAll();
      setRecords(data);
    } catch (error) {
      console.error('Failed to fetch maintenance records:', error);
      // Mock data for demo
      setRecords([
        {
          id: 1,
          vehicle_id: 1,
          vehicle_plate: 'VH001',
          type: 'Oil Change',
          description: 'Regular oil change and filter replacement',
          cost: 150.00,
          scheduled_date: '2024-01-15',
          completed_date: '2024-01-15',
          status: 'Completed',
          mechanic: 'John Mechanic',
          next_service: '2024-04-15'
        },
        {
          id: 2,
          vehicle_id: 2,
          vehicle_plate: 'VH002',
          type: 'Brake Service',
          description: 'Brake pad replacement and brake fluid change',
          cost: 350.00,
          scheduled_date: '2024-01-20',
          status: 'Scheduled',
          mechanic: 'Mike Service'
        },
        {
          id: 3,
          vehicle_id: 1,
          vehicle_plate: 'VH001',
          type: 'Tire Rotation',
          description: 'Rotate tires and check tire pressure',
          cost: 80.00,
          scheduled_date: '2024-01-18',
          status: 'In Progress',
          mechanic: 'Sarah Tech'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleScheduleMaintenance = () => {
    setEditingRecord(null);
    setFormData({
      vehicle_id: 1,
      vehicle_plate: 'VH001',
      type: 'Oil Change',
      description: '',
      cost: 0,
      scheduled_date: new Date().toISOString().split('T')[0],
      status: 'Scheduled',
      mechanic: ''
    });
    setShowModal(true);
  };

  const handleEditRecord = (record: MaintenanceRecord) => {
    setEditingRecord(record);
    setFormData({
      vehicle_id: record.vehicle_id,
      vehicle_plate: record.vehicle_plate || '',
      type: record.type,
      description: record.description,
      cost: record.cost,
      scheduled_date: record.scheduled_date,
      status: record.status,
      mechanic: record.mechanic || ''
    });
    setShowModal(true);
  };

  const handleSaveRecord = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      if (editingRecord) {
        // Update existing record
        await maintenanceAPI.update(editingRecord.id, formData);
        console.log('✅ Maintenance record updated successfully');
      } else {
        // Add new record
        await maintenanceAPI.create(formData);
        console.log('✅ Maintenance record created successfully');
      }

      // Refresh the records list
      await fetchRecords();
      setShowModal(false);
      setEditingRecord(null);
    } catch (error) {
      console.error('❌ Failed to save maintenance record:', error);
      alert('Failed to save maintenance record. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleStartService = async (id: string) => {
    try {
      await maintenanceAPI.update(id, { status: 'In Progress' });
      console.log('✅ Maintenance service started');
      await fetchRecords();
    } catch (error) {
      console.error('❌ Failed to start service:', error);
      alert('Failed to start service. Please try again.');
    }
  };

  const handleCompleteService = async (id: string) => {
    try {
      await maintenanceAPI.update(id, {
        status: 'Completed',
        completed_date: new Date().toISOString().split('T')[0]
      });
      console.log('✅ Maintenance service completed');
      await fetchRecords();
    } catch (error) {
      console.error('❌ Failed to complete service:', error);
      alert('Failed to complete service. Please try again.');
    }
  };

  const filteredRecords = records.filter(record => {
    const matchesSearch = record.vehicle_plate?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || record.status.toLowerCase() === statusFilter.toLowerCase();
    const matchesType = typeFilter === 'all' || record.type.toLowerCase().includes(typeFilter.toLowerCase());
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'oil change':
        return Wrench;
      case 'brake service':
        return AlertTriangle;
      default:
        return Wrench;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Maintenance Management</h1>
        <button 
          onClick={handleScheduleMaintenance}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Schedule Maintenance</span>
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row space-y-4 lg:space-y-0 lg:space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search maintenance records..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="flex space-x-4">
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="scheduled">Scheduled</option>
              <option value="in progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="overdue">Overdue</option>
            </select>
          </div>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Types</option>
            <option value="oil">Oil Change</option>
            <option value="brake">Brake Service</option>
            <option value="tire">Tire Service</option>
            <option value="engine">Engine Service</option>
          </select>
        </div>
      </div>

      {/* Maintenance Records */}
      <div className="space-y-4">
        {filteredRecords.map((record) => {
          const TypeIcon = getTypeIcon(record.type);
          
          return (
            <div key={record.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                      <TypeIcon className="h-6 w-6 text-orange-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{record.type}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                          {record.status}
                        </span>
                      </div>
                      <p className="text-gray-600 mb-2">{record.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Car className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">Vehicle: {record.vehicle_plate}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">
                            Scheduled: {new Date(record.scheduled_date).toLocaleDateString()}
                          </span>
                        </div>
                        {record.completed_date && (
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">
                              Completed: {new Date(record.completed_date).toLocaleDateString()}
                            </span>
                          </div>
                        )}
                        {record.mechanic && (
                          <div className="flex items-center space-x-2">
                            <Wrench className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Mechanic: {record.mechanic}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">${record.cost.toFixed(2)}</div>
                    {record.next_service && (
                      <div className="text-sm text-gray-500">
                        Next: {new Date(record.next_service).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>

                {record.status.toLowerCase() === 'scheduled' && (
                  <div className="flex space-x-2 mt-4">
                    <button 
                      onClick={() => handleStartService(record.id)}
                      className="bg-blue-50 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-100 transition-colors"
                    >
                      Start Service
                    </button>
                    <button 
                      onClick={() => handleEditRecord(record)}
                      className="bg-gray-50 text-gray-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      Reschedule
                    </button>
                  </div>
                )}
                
                {record.status.toLowerCase() === 'in progress' && (
                  <div className="flex space-x-2 mt-4">
                    <button 
                      onClick={() => handleCompleteService(record.id)}
                      className="bg-green-50 text-green-600 px-4 py-2 rounded-lg hover:bg-green-100 transition-colors"
                    >
                      Mark Complete
                    </button>
                    <button 
                      onClick={() => handleEditRecord(record)}
                      className="bg-gray-50 text-gray-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      Add Notes
                    </button>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {filteredRecords.length === 0 && (
        <div className="text-center py-12">
          <Wrench className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No maintenance records found</h3>
          <p className="mt-1 text-sm text-gray-500">Schedule maintenance for your vehicles to get started.</p>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {editingRecord ? 'Edit Maintenance' : 'Schedule Maintenance'}
            </h2>
            
            <form onSubmit={handleSaveRecord} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Vehicle
                </label>
                <select
                  value={formData.vehicle_plate}
                  onChange={(e) => setFormData(prev => ({ ...prev, vehicle_plate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="VH001">VH001 - Toyota Camry</option>
                  <option value="VH002">VH002 - Honda Civic</option>
                  <option value="VH003">VH003 - Ford Focus</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Maintenance Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="Oil Change">Oil Change</option>
                  <option value="Brake Service">Brake Service</option>
                  <option value="Tire Rotation">Tire Rotation</option>
                  <option value="Engine Service">Engine Service</option>
                  <option value="Transmission Service">Transmission Service</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  required
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Estimated Cost
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.cost}
                    onChange={(e) => setFormData(prev => ({ ...prev, cost: parseFloat(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Scheduled Date
                  </label>
                  <input
                    type="date"
                    value={formData.scheduled_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, scheduled_date: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mechanic
                </label>
                <input
                  type="text"
                  value={formData.mechanic}
                  onChange={(e) => setFormData(prev => ({ ...prev, mechanic: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {saving ? 'Saving...' : (editingRecord ? 'Update' : 'Schedule')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Maintenance;