const supabase = require('../utils/supabaseClient');

const getAllDrivers = async (req, res) => {
  try {
    const { branch_id, status, page = 1, limit = 50 } = req.query;
    
    let query = supabase
      .from('drivers')
      .select(`
        *,
        branch:branches(name),
        manager:users!drivers_manager_id_fkey(name),
        assigned_vehicle:vehicles(license_plate, make, model)
      `);

    // Apply filters
    if (branch_id) {
      query = query.eq('branch_id', branch_id);
    }
    
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch drivers',
        error: error.message
      });
    }

    res.json({
      success: true,
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get drivers error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getDriverById = async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('drivers')
      .select(`
        *,
        branch:branches(name, location),
        manager:users!drivers_manager_id_fkey(name),
        assigned_vehicle:vehicles(license_plate, make, model, vehicle_status),
        fuel_records:fuel(
          id,
          datetime,
          fuel_type,
          quantity,
          amount,
          station
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        message: 'Driver not found',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get driver error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const createDriver = async (req, res) => {
  try {
    const driverData = req.body;

    // Validate required fields
    const requiredFields = ['code', 'name_en', 'name_ar'];
    for (const field of requiredFields) {
      if (!driverData[field]) {
        return res.status(400).json({
          success: false,
          message: `${field} is required`
        });
      }
    }

    const { data, error } = await supabase
      .from('drivers')
      .insert(driverData)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create driver',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'Driver created successfully',
      data
    });
  } catch (error) {
    console.error('Create driver error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const updateDriver = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.id;
    delete updates.created_at;

    const { data, error } = await supabase
      .from('drivers')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update driver',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Driver updated successfully',
      data
    });
  } catch (error) {
    console.error('Update driver error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const deleteDriver = async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('drivers')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to delete driver',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Driver deleted successfully'
    });
  } catch (error) {
    console.error('Delete driver error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getAllDrivers,
  getDriverById,
  createDriver,
  updateDriver,
  deleteDriver
};