/*
  # Fix RLS Policies and Database Schema

  1. Create all necessary tables with proper structure
  2. Set up optimized RLS policies without OLD references
  3. Create helper functions for role-based access
  4. Add performance indexes
  5. Set up automatic timestamp triggers

  This migration ensures all tables exist and have proper security policies.
*/

-- First, ensure all tables exist (safety check)

-- Create users table if it doesn't exist
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  auth_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  email text UNIQUE NOT NULL,
  role text NOT NULL CHECK (role IN ('Super Admin', 'Admin', 'Manager', 'Driver')),
  status text NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
  manager_id uuid REFERENCES users(id),
  branch_id uuid,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create branches table if it doesn't exist
CREATE TABLE IF NOT EXISTS branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  location text NOT NULL,
  address text,
  phone text,
  email text,
  manager_id uuid REFERENCES users(id),
  status text NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Add foreign key constraint for users.branch_id if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'fk_users_branch' 
    AND table_name = 'users'
  ) THEN
    ALTER TABLE users ADD CONSTRAINT fk_users_branch 
      FOREIGN KEY (branch_id) REFERENCES branches(id);
  END IF;
END $$;

-- Create drivers table if it doesn't exist
CREATE TABLE IF NOT EXISTS drivers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code integer UNIQUE NOT NULL,
  name_en text NOT NULL,
  name_ar text NOT NULL,
  work_number text,
  personal_number text,
  user_profile_url text,
  username text UNIQUE,
  license_number text,
  license_expiry date,
  phone text,
  email text,
  hire_date date DEFAULT CURRENT_DATE,
  branch_id uuid REFERENCES branches(id),
  manager_id uuid REFERENCES users(id),
  user_id uuid REFERENCES users(id),
  status text NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive', 'Suspended')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create vehicles table if it doesn't exist
CREATE TABLE IF NOT EXISTS vehicles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id text UNIQUE NOT NULL,
  license_plate text UNIQUE NOT NULL,
  service_type text,
  vehicle_type text,
  make text,
  model text,
  year integer,
  color text,
  vin_number text UNIQUE,
  fuel_type text NOT NULL CHECK (fuel_type IN ('Gasoline', 'Diesel', 'Electric', 'Hybrid', '92', '95')),
  current_km integer DEFAULT 0,
  vehicle_status text NOT NULL DEFAULT 'Active' CHECK (vehicle_status IN ('Active', 'Inactive', 'Maintenance', 'Out of Service')),
  branch_id uuid REFERENCES branches(id),
  current_location text,
  driver_id uuid REFERENCES drivers(id),
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create thresholds table if it doesn't exist
CREATE TABLE IF NOT EXISTS thresholds (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code text UNIQUE NOT NULL,
  check_type text NOT NULL CHECK (check_type IN ('Maintenance', 'Tier', 'Fuel', 'Driver')),
  car_type text,
  parameter text NOT NULL,
  condition_type text NOT NULL DEFAULT 'greater_than' CHECK (condition_type IN ('greater_than', 'less_than', 'days_since', 'days_until')),
  value integer NOT NULL,
  unit text DEFAULT 'km',
  description text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create maintenance table if it doesn't exist
CREATE TABLE IF NOT EXISTS maintenance (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id uuid REFERENCES vehicles(id) ON DELETE CASCADE,
  license_plate text,
  service_type text NOT NULL,
  service_center text,
  description text,
  odometer_reading integer,
  parts_cost decimal(10,2) DEFAULT 0,
  labor_cost decimal(10,2) DEFAULT 0,
  total_cost decimal(10,2) DEFAULT 0,
  scheduled_date date,
  service_date date,
  next_service_date date,
  completed_date date,
  status text NOT NULL DEFAULT 'Scheduled' CHECK (status IN ('Scheduled', 'In Progress', 'Completed', 'Cancelled', 'Overdue')),
  mechanic text,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create fuel table if it doesn't exist
CREATE TABLE IF NOT EXISTS fuel (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id uuid REFERENCES vehicles(id) ON DELETE CASCADE,
  license_plate text,
  vehicle_type text,
  vehicle_status text,
  vin text,
  datetime timestamptz DEFAULT now(),
  driver_id uuid REFERENCES drivers(id),
  driver_name_en text,
  driver_name_ar text,
  fuel_type text NOT NULL,
  amount decimal(10,2) NOT NULL,
  quantity decimal(10,2) NOT NULL,
  cost_per_liter decimal(10,2),
  pump_image_reading decimal(10,2),
  difference decimal(10,2),
  odometer integer,
  distance integer,
  consumption_rate decimal(5,2),
  cost_per_meter decimal(10,4),
  branch_id uuid REFERENCES branches(id),
  vehicle_group text,
  vehicle_model integer,
  station text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE thresholds ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuel ENABLE ROW LEVEL SECURITY;

-- Create helper functions first (before policies that use them)
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM users 
    WHERE auth_id = user_id 
    AND role IN ('Super Admin', 'Admin')
    LIMIT 1
  );
$$;

CREATE OR REPLACE FUNCTION can_manage_drivers(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM users 
    WHERE auth_id = user_id 
    AND role IN ('Super Admin', 'Admin', 'Manager')
    LIMIT 1
  );
$$;

-- Drop all existing policies first
DROP POLICY IF EXISTS "Users can read own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Admins can read all users" ON users;
DROP POLICY IF EXISTS "Admins can create users" ON users;
DROP POLICY IF EXISTS "Admins can update users" ON users;
DROP POLICY IF EXISTS "Super Admins can delete users" ON users;
DROP POLICY IF EXISTS "users_select_own" ON users;
DROP POLICY IF EXISTS "users_update_own" ON users;
DROP POLICY IF EXISTS "users_insert_new" ON users;
DROP POLICY IF EXISTS "admins_select_all_users" ON users;
DROP POLICY IF EXISTS "admins_insert_users" ON users;
DROP POLICY IF EXISTS "admins_update_users" ON users;
DROP POLICY IF EXISTS "super_admins_delete_users" ON users;

-- Create new optimized policies for users table
CREATE POLICY "users_select_own"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = auth_id);

CREATE POLICY "users_update_own"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = auth_id)
  WITH CHECK (auth.uid() = auth_id);

CREATE POLICY "users_insert_new"
  ON users
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = auth_id);

CREATE POLICY "admins_select_all_users"
  ON users
  FOR SELECT
  TO authenticated
  USING (is_admin(auth.uid()));

CREATE POLICY "admins_insert_users"
  ON users
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "admins_update_users"
  ON users
  FOR UPDATE
  TO authenticated
  USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "super_admins_delete_users"
  ON users
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE auth_id = auth.uid() 
      AND role = 'Super Admin'
      LIMIT 1
    )
  );

-- Update branches policies
DROP POLICY IF EXISTS "Users can read branches" ON branches;
DROP POLICY IF EXISTS "Admins can manage branches" ON branches;
DROP POLICY IF EXISTS "branches_select_all" ON branches;
DROP POLICY IF EXISTS "admins_manage_branches" ON branches;

CREATE POLICY "branches_select_all"
  ON branches
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "admins_manage_branches"
  ON branches
  FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

-- Update drivers policies
DROP POLICY IF EXISTS "Users can read drivers in their branch" ON drivers;
DROP POLICY IF EXISTS "Managers can manage drivers" ON drivers;
DROP POLICY IF EXISTS "drivers_select_by_branch" ON drivers;
DROP POLICY IF EXISTS "managers_manage_drivers" ON drivers;

CREATE POLICY "drivers_select_by_branch"
  ON drivers
  FOR SELECT
  TO authenticated
  USING (
    is_admin(auth.uid()) OR
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.auth_id = auth.uid()
      AND (u.branch_id = drivers.branch_id OR u.role IN ('Super Admin', 'Admin'))
    )
  );

CREATE POLICY "managers_manage_drivers"
  ON drivers
  FOR ALL
  TO authenticated
  USING (can_manage_drivers(auth.uid()))
  WITH CHECK (can_manage_drivers(auth.uid()));

-- Update vehicles policies
DROP POLICY IF EXISTS "Users can read vehicles in their branch" ON vehicles;
DROP POLICY IF EXISTS "Managers can manage vehicles" ON vehicles;
DROP POLICY IF EXISTS "vehicles_select_by_branch" ON vehicles;
DROP POLICY IF EXISTS "managers_manage_vehicles" ON vehicles;

CREATE POLICY "vehicles_select_by_branch"
  ON vehicles
  FOR SELECT
  TO authenticated
  USING (
    is_admin(auth.uid()) OR
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.auth_id = auth.uid()
      AND (u.branch_id = vehicles.branch_id OR u.role IN ('Super Admin', 'Admin'))
    )
  );

CREATE POLICY "managers_manage_vehicles"
  ON vehicles
  FOR ALL
  TO authenticated
  USING (can_manage_drivers(auth.uid()))
  WITH CHECK (can_manage_drivers(auth.uid()));

-- Update thresholds policies
DROP POLICY IF EXISTS "Users can read thresholds" ON thresholds;
DROP POLICY IF EXISTS "Admins can manage thresholds" ON thresholds;
DROP POLICY IF EXISTS "thresholds_select_all" ON thresholds;
DROP POLICY IF EXISTS "admins_manage_thresholds" ON thresholds;

CREATE POLICY "thresholds_select_all"
  ON thresholds
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "admins_manage_thresholds"
  ON thresholds
  FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

-- Update maintenance policies
DROP POLICY IF EXISTS "Users can read maintenance records" ON maintenance;
DROP POLICY IF EXISTS "Managers can manage maintenance" ON maintenance;
DROP POLICY IF EXISTS "maintenance_select_by_access" ON maintenance;
DROP POLICY IF EXISTS "managers_manage_maintenance" ON maintenance;

CREATE POLICY "maintenance_select_by_access"
  ON maintenance
  FOR SELECT
  TO authenticated
  USING (
    is_admin(auth.uid()) OR
    EXISTS (
      SELECT 1 FROM users u
      JOIN vehicles v ON v.branch_id = u.branch_id
      WHERE u.auth_id = auth.uid()
      AND v.id = maintenance.vehicle_id
    )
  );

CREATE POLICY "managers_manage_maintenance"
  ON maintenance
  FOR ALL
  TO authenticated
  USING (can_manage_drivers(auth.uid()))
  WITH CHECK (can_manage_drivers(auth.uid()));

-- Update fuel policies
DROP POLICY IF EXISTS "Users can read fuel records" ON fuel;
DROP POLICY IF EXISTS "Drivers can add fuel records" ON fuel;
DROP POLICY IF EXISTS "Managers can manage fuel records" ON fuel;
DROP POLICY IF EXISTS "fuel_select_by_access" ON fuel;
DROP POLICY IF EXISTS "authenticated_users_insert_fuel" ON fuel;
DROP POLICY IF EXISTS "managers_manage_fuel" ON fuel;

CREATE POLICY "fuel_select_by_access"
  ON fuel
  FOR SELECT
  TO authenticated
  USING (
    is_admin(auth.uid()) OR
    EXISTS (
      SELECT 1 FROM users u
      JOIN vehicles v ON v.branch_id = u.branch_id
      WHERE u.auth_id = auth.uid()
      AND v.id = fuel.vehicle_id
    )
  );

CREATE POLICY "authenticated_users_insert_fuel"
  ON fuel
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "managers_manage_fuel"
  ON fuel
  FOR ALL
  TO authenticated
  USING (can_manage_drivers(auth.uid()))
  WITH CHECK (can_manage_drivers(auth.uid()));

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_auth_id ON users(auth_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_branch_id ON users(branch_id);
CREATE INDEX IF NOT EXISTS idx_users_branch_role ON users(branch_id, role);
CREATE INDEX IF NOT EXISTS idx_drivers_code ON drivers(code);
CREATE INDEX IF NOT EXISTS idx_drivers_branch_id ON drivers(branch_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_license_plate ON vehicles(license_plate);
CREATE INDEX IF NOT EXISTS idx_vehicles_branch_id ON vehicles(branch_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_driver_id ON vehicles(driver_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_vehicle_id ON maintenance(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_service_date ON maintenance(service_date);
CREATE INDEX IF NOT EXISTS idx_fuel_vehicle_id ON fuel(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_fuel_datetime ON fuel(datetime);

-- Create function for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for automatic timestamp updates
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_branches_updated_at ON branches;
CREATE TRIGGER update_branches_updated_at
    BEFORE UPDATE ON branches
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_drivers_updated_at ON drivers;
CREATE TRIGGER update_drivers_updated_at
    BEFORE UPDATE ON drivers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_vehicles_updated_at ON vehicles;
CREATE TRIGGER update_vehicles_updated_at
    BEFORE UPDATE ON vehicles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_maintenance_updated_at ON maintenance;
CREATE TRIGGER update_maintenance_updated_at
    BEFORE UPDATE ON maintenance
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_fuel_updated_at ON fuel;
CREATE TRIGGER update_fuel_updated_at
    BEFORE UPDATE ON fuel
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_thresholds_updated_at ON thresholds;
CREATE TRIGGER update_thresholds_updated_at
    BEFORE UPDATE ON thresholds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();