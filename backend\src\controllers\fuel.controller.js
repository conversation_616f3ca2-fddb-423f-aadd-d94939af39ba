const supabase = require('../utils/supabaseClient');

const getAllFuel = async (req, res) => {
  try {
    const { vehicle_id, driver_id, date_from, date_to, page = 1, limit = 50 } = req.query;
    
    let query = supabase
      .from('fuel')
      .select(`
        *,
        vehicle:vehicles(license_plate, make, model),
        driver:drivers(name_en, name_ar),
        branch:branches(name)
      `);

    // Apply filters
    if (vehicle_id) {
      query = query.eq('vehicle_id', vehicle_id);
    }
    
    if (driver_id) {
      query = query.eq('driver_id', driver_id);
    }

    if (date_from) {
      query = query.gte('datetime', date_from);
    }

    if (date_to) {
      query = query.lte('datetime', date_to);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    query = query.order('datetime', { ascending: false });

    const { data, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch fuel records',
        error: error.message
      });
    }

    res.json({
      success: true,
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get fuel error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getFuelById = async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('fuel')
      .select(`
        *,
        vehicle:vehicles(
          license_plate,
          make,
          model,
          current_km,
          branch:branches(name)
        ),
        driver:drivers(name_en, name_ar, phone)
      `)
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        message: 'Fuel record not found',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get fuel error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const createFuel = async (req, res) => {
  try {
    const fuelData = req.body;

    // Validate required fields
    const requiredFields = ['vehicle_id', 'fuel_type', 'amount', 'quantity'];
    for (const field of requiredFields) {
      if (!fuelData[field]) {
        return res.status(400).json({
          success: false,
          message: `${field} is required`
        });
      }
    }

    // Calculate cost per liter if not provided
    if (!fuelData.cost_per_liter && fuelData.amount && fuelData.quantity) {
      fuelData.cost_per_liter = fuelData.amount / fuelData.quantity;
    }

    // Calculate consumption rate if distance is provided
    if (fuelData.distance && fuelData.quantity) {
      fuelData.consumption_rate = (fuelData.quantity / fuelData.distance) * 100; // L/100km
    }

    const { data, error } = await supabase
      .from('fuel')
      .insert(fuelData)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create fuel record',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'Fuel record created successfully',
      data
    });
  } catch (error) {
    console.error('Create fuel error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const updateFuel = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.id;
    delete updates.created_at;

    // Recalculate cost per liter if amount or quantity changed
    if (updates.amount !== undefined || updates.quantity !== undefined) {
      const { data: current } = await supabase
        .from('fuel')
        .select('amount, quantity')
        .eq('id', id)
        .single();

      if (current) {
        const amount = updates.amount ?? current.amount;
        const quantity = updates.quantity ?? current.quantity;
        if (quantity > 0) {
          updates.cost_per_liter = amount / quantity;
        }
      }
    }

    const { data, error } = await supabase
      .from('fuel')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update fuel record',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Fuel record updated successfully',
      data
    });
  } catch (error) {
    console.error('Update fuel error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const deleteFuel = async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('fuel')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to delete fuel record',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Fuel record deleted successfully'
    });
  } catch (error) {
    console.error('Delete fuel error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getFuelStats = async (req, res) => {
  try {
    const { date_from, date_to } = req.query;
    
    let query = supabase
      .from('fuel')
      .select('fuel_type, amount, quantity, datetime, vehicle_id');

    if (date_from) {
      query = query.gte('datetime', date_from);
    }

    if (date_to) {
      query = query.lte('datetime', date_to);
    }

    const { data: fuel, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch fuel stats',
        error: error.message
      });
    }

    const stats = {
      total_records: fuel.length,
      total_amount: fuel.reduce((sum, record) => sum + (record.amount || 0), 0),
      total_quantity: fuel.reduce((sum, record) => sum + (record.quantity || 0), 0),
      by_fuel_type: fuel.reduce((acc, record) => {
        if (!acc[record.fuel_type]) {
          acc[record.fuel_type] = { count: 0, amount: 0, quantity: 0 };
        }
        acc[record.fuel_type].count++;
        acc[record.fuel_type].amount += record.amount || 0;
        acc[record.fuel_type].quantity += record.quantity || 0;
        return acc;
      }, {}),
      average_cost_per_liter: fuel.length > 0 
        ? fuel.reduce((sum, record) => sum + (record.amount || 0), 0) / 
          fuel.reduce((sum, record) => sum + (record.quantity || 0), 0)
        : 0
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get fuel stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getAllFuel,
  getFuelById,
  createFuel,
  updateFuel,
  deleteFuel,
  getFuelStats
};