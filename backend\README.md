# Fleet Management System - Backend API

A comprehensive backend API for the Fleet Management System built with Node.js, Express, and Supabase.

## 🚀 Features

- **RESTful API** with comprehensive endpoints for all fleet management operations
- **Supabase Integration** for database operations and authentication
- **Role-based Access Control** with JWT authentication
- **Comprehensive Logging** and error handling
- **Input Validation** and sanitization
- **CORS Support** for frontend integration
- **Health Monitoring** endpoints
- **Scalable Architecture** with clean separation of concerns

## 📋 Prerequisites

- Node.js 18+ and npm 8+
- Supabase project with configured database
- Environment variables configured

## 🛠️ Installation

1. **Clone and navigate to backend directory:**
```bash
cd backend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Configure environment variables:**
```bash
cp .env.example .env
```

Edit `.env` with your Supabase credentials:
```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
```

4. **Start the development server:**
```bash
npm run dev
```

The API will be available at `http://localhost:5000`

## 📚 API Documentation

### Base URL
```
http://localhost:5000/api
```

### Authentication
All endpoints (except health check) require authentication via JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### Endpoints Overview

#### Health & Info
- `GET /api/health` - Health check
- `GET /api` - API information and endpoints list

#### Users Management
- `GET /api/users` - Get all users (Admin+)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create new user (Admin+)
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (Super Admin)

#### Vehicles Management
- `GET /api/vehicles` - Get all vehicles
- `GET /api/vehicles/stats` - Get vehicle statistics
- `GET /api/vehicles/:id` - Get vehicle by ID
- `POST /api/vehicles` - Create new vehicle (Manager+)
- `PUT /api/vehicles/:id` - Update vehicle (Manager+)
- `DELETE /api/vehicles/:id` - Delete vehicle (Admin+)

#### Drivers Management
- `GET /api/drivers` - Get all drivers
- `GET /api/drivers/:id` - Get driver by ID
- `POST /api/drivers` - Create new driver (Manager+)
- `PUT /api/drivers/:id` - Update driver (Manager+)
- `DELETE /api/drivers/:id` - Delete driver (Admin+)

#### Branches Management
- `GET /api/branches` - Get all branches
- `GET /api/branches/:id` - Get branch by ID
- `POST /api/branches` - Create new branch (Admin+)
- `PUT /api/branches/:id` - Update branch (Admin+)
- `DELETE /api/branches/:id` - Delete branch (Super Admin)

#### Maintenance Management
- `GET /api/maintenance` - Get all maintenance records
- `GET /api/maintenance/stats` - Get maintenance statistics
- `GET /api/maintenance/:id` - Get maintenance by ID
- `POST /api/maintenance` - Create maintenance record (Manager+)
- `PUT /api/maintenance/:id` - Update maintenance record (Manager+)
- `DELETE /api/maintenance/:id` - Delete maintenance record (Admin+)

#### Fuel Management
- `GET /api/fuel` - Get all fuel records
- `GET /api/fuel/stats` - Get fuel statistics
- `GET /api/fuel/:id` - Get fuel record by ID
- `POST /api/fuel` - Create fuel record (All users)
- `PUT /api/fuel/:id` - Update fuel record (Manager+)
- `DELETE /api/fuel/:id` - Delete fuel record (Admin+)

#### Thresholds Management
- `GET /api/thresholds` - Get all thresholds
- `GET /api/thresholds/:id` - Get threshold by ID
- `POST /api/thresholds` - Create threshold (Admin+)
- `PUT /api/thresholds/:id` - Update threshold (Admin+)
- `DELETE /api/thresholds/:id` - Delete threshold (Super Admin)

#### Reports
- `GET /api/reports/dashboard` - Get dashboard statistics
- `GET /api/reports/vehicles` - Get vehicle report (Manager+)
- `GET /api/reports/maintenance` - Get maintenance report (Manager+)
- `GET /api/reports/fuel` - Get fuel report (Manager+)
- `GET /api/reports/drivers` - Get driver report (Manager+)

### Query Parameters

Most GET endpoints support filtering and pagination:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 50)
- `status` - Filter by status
- `branch_id` - Filter by branch
- `date_from` - Filter from date (YYYY-MM-DD)
- `date_to` - Filter to date (YYYY-MM-DD)

Example:
```
GET /api/vehicles?page=1&limit=20&status=Active&branch_id=123
```

### Response Format

All API responses follow this format:

**Success Response:**
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 100
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

## 🔐 Security Features

### Role-Based Access Control

The API implements four user roles with different permissions:

1. **Super Admin** - Full access to all operations
2. **Admin** - Manage users, vehicles, drivers, branches (except user deletion)
3. **Manager** - Manage vehicles, drivers, maintenance, fuel records
4. **Driver** - View assigned vehicles, add fuel records

### Authentication Flow

1. User authenticates via Supabase Auth
2. JWT token is provided for subsequent requests
3. Middleware validates token and loads user profile
4. Role-based permissions are enforced per endpoint

### Security Middleware

- **CORS** - Configured for frontend domain
- **Rate Limiting** - Prevents API abuse
- **Input Validation** - Validates all request data
- **Error Handling** - Secure error responses

## 🗄️ Database Integration

### Supabase Configuration

The API uses Supabase for:
- **Authentication** - JWT token validation
- **Database Operations** - PostgreSQL with Row Level Security
- **Real-time Updates** - WebSocket connections for live data

### Row Level Security (RLS)

Database policies ensure users can only access data they're authorized to see:
- Users see data from their branch or all data if Admin+
- Drivers can only add fuel records, not modify others
- Managers can manage resources in their scope

## 🚀 Deployment

### Environment Setup

1. **Production Environment Variables:**
```env
NODE_ENV=production
SUPABASE_URL=your_production_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_production_service_key
PORT=5000
FRONTEND_URL=https://your-frontend-domain.com
```

2. **Build and Start:**
```bash
npm install --production
npm start
```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

### Health Monitoring

Monitor API health via:
- `GET /api/health` - Basic health check
- Application logs for detailed monitoring
- Supabase dashboard for database metrics

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix
```

## 📊 Performance Considerations

- **Database Indexing** - Optimized queries with proper indexes
- **Pagination** - All list endpoints support pagination
- **Caching** - Response caching for static data
- **Connection Pooling** - Efficient database connections

## 🔧 Development

### Project Structure
```
backend/
├── src/
│   ├── controllers/     # Request handlers
│   ├── routes/         # API routes
│   ├── middlewares/    # Authentication & validation
│   ├── utils/          # Utilities and helpers
│   ├── app.js          # Express app configuration
│   └── server.js       # Server startup
├── .env.example        # Environment template
├── package.json        # Dependencies
└── README.md          # This file
```

### Adding New Endpoints

1. Create controller in `src/controllers/`
2. Define routes in `src/routes/`
3. Add authentication/authorization middleware
4. Update API documentation

### Error Handling

The API uses centralized error handling:
- Database errors are caught and formatted
- Validation errors return detailed messages
- Authentication errors return appropriate status codes
- All errors are logged for debugging

## 📞 Support

For issues and questions:
1. Check the API health endpoint
2. Review application logs
3. Verify Supabase configuration
4. Check environment variables

## 🔄 API Versioning

Current version: **v1.0.0**

Future versions will be handled via URL versioning:
- `/api/v1/` - Current version
- `/api/v2/` - Future version

---

**Built with ❤️ for efficient fleet management**