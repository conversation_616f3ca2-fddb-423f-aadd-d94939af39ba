const supabase = require('../utils/supabaseClient');

const getAllVehicles = async (req, res) => {
  try {
    const { branch_id, status, page = 1, limit = 50 } = req.query;
    
    let query = supabase
      .from('vehicles')
      .select(`
        *,
        branch:branches(name),
        driver:drivers(name_en, name_ar, phone)
      `);

    // Apply filters
    if (branch_id) {
      query = query.eq('branch_id', branch_id);
    }
    
    if (status) {
      query = query.eq('vehicle_status', status);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch vehicles',
        error: error.message
      });
    }

    res.json({
      success: true,
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count
      }
    });
  } catch (error) {
    console.error('Get vehicles error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getVehicleById = async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('vehicles')
      .select(`
        *,
        branch:branches(name, location),
        driver:drivers(name_en, name_ar, phone, email),
        maintenance_records:maintenance(
          id,
          service_type,
          status,
          scheduled_date,
          total_cost
        ),
        fuel_records:fuel(
          id,
          datetime,
          fuel_type,
          quantity,
          amount
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get vehicle error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const createVehicle = async (req, res) => {
  try {
    const vehicleData = req.body;

    // Validate required fields
    const requiredFields = ['vehicle_id', 'license_plate', 'fuel_type'];
    for (const field of requiredFields) {
      if (!vehicleData[field]) {
        return res.status(400).json({
          success: false,
          message: `${field} is required`
        });
      }
    }

    const { data, error } = await supabase
      .from('vehicles')
      .insert(vehicleData)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create vehicle',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'Vehicle created successfully',
      data
    });
  } catch (error) {
    console.error('Create vehicle error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const updateVehicle = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.id;
    delete updates.created_at;

    const { data, error } = await supabase
      .from('vehicles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update vehicle',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Vehicle updated successfully',
      data
    });
  } catch (error) {
    console.error('Update vehicle error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const deleteVehicle = async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('vehicles')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to delete vehicle',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Vehicle deleted successfully'
    });
  } catch (error) {
    console.error('Delete vehicle error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getVehicleStats = async (req, res) => {
  try {
    const { data: vehicles, error } = await supabase
      .from('vehicles')
      .select('vehicle_status, fuel_type, branch_id');

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch vehicle stats',
        error: error.message
      });
    }

    const stats = {
      total: vehicles.length,
      byStatus: vehicles.reduce((acc, vehicle) => {
        acc[vehicle.vehicle_status] = (acc[vehicle.vehicle_status] || 0) + 1;
        return acc;
      }, {}),
      byFuelType: vehicles.reduce((acc, vehicle) => {
        acc[vehicle.fuel_type] = (acc[vehicle.fuel_type] || 0) + 1;
        return acc;
      }, {}),
      byBranch: vehicles.reduce((acc, vehicle) => {
        if (vehicle.branch_id) {
          acc[vehicle.branch_id] = (acc[vehicle.branch_id] || 0) + 1;
        }
        return acc;
      }, {})
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get vehicle stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getAllVehicles,
  getVehicleById,
  createVehicle,
  updateVehicle,
  deleteVehicle,
  getVehicleStats
};