/*
  # إدراج البيانات الأولية

  1. البيانات الأولية
    - إدراج المستخدمين الأساسيين
    - إدراج الفروع
    - إدراج السائقين
    - إدراج المركبات
    - إدراج العتبات
    - إدراج بيانات الصيانة
    - إدراج بيانات الوقود

  2. ملاحظات
    - يجب تشغيل هذا الملف بعد إنشاء المخطط
    - البيانات مأخوذة من قاعدة البيانات الأصلية
*/

-- إدراج الفروع أولاً
INSERT INTO branches (id, name, location, address, phone, email, status) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Cairo', 'Cairo, Egypt', '123 Main Street, Downtown, Cairo', '+20 2 1234567', '<EMAIL>', 'Active'),
('550e8400-e29b-41d4-a716-446655440002', 'Alexandria', 'Alexandria, Egypt', '456 North Avenue, Alexandria', '+20 3 9876543', '<EMAIL>', 'Active'),
('550e8400-e29b-41d4-a716-446655440003', 'Gouna', 'Gouna, Egypt', '789 West Boulevard, El Gouna', '+20 65 4567890', '<EMAIL>', 'Active'),
('550e8400-e29b-41d4-a716-446655440004', '6th October', '6th October City, Egypt', '321 October Street, 6th October', '+20 2 3456789', '<EMAIL>', 'Active'),
('550e8400-e29b-41d4-a716-446655440005', 'Nasr City', 'Nasr City, Cairo', '654 Nasr Road, Nasr City', '+20 2 6543210', '<EMAIL>', 'Active');

-- إدراج المستخدمين
INSERT INTO users (id, name, email, role, status, branch_id) VALUES
('550e8400-e29b-41d4-a716-446655440010', 'Mohamed Taha', '<EMAIL>', 'Super Admin', 'Active', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440011', 'Hossam Ali', '<EMAIL>', 'Admin', 'Active', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440012', 'Ahmed Hassan', '<EMAIL>', 'Manager', 'Active', '550e8400-e29b-41d4-a716-446655440002'),
('550e8400-e29b-41d4-a716-446655440013', 'Sara Youssef', '<EMAIL>', 'Driver', 'Active', '550e8400-e29b-41d4-a716-446655440003'),
('550e8400-e29b-41d4-a716-446655440014', 'Fatma Adel', '<EMAIL>', 'Driver', 'Inactive', '550e8400-e29b-41d4-a716-446655440003');

-- تحديث مدراء الفروع
UPDATE branches SET manager_id = '550e8400-e29b-41d4-a716-446655440011' WHERE id = '550e8400-e29b-41d4-a716-446655440001';
UPDATE branches SET manager_id = '550e8400-e29b-41d4-a716-446655440012' WHERE id = '550e8400-e29b-41d4-a716-446655440002';
UPDATE branches SET manager_id = '550e8400-e29b-41d4-a716-446655440011' WHERE id = '550e8400-e29b-41d4-a716-446655440003';
UPDATE branches SET manager_id = '550e8400-e29b-41d4-a716-446655440012' WHERE id = '550e8400-e29b-41d4-a716-446655440004';
UPDATE branches SET manager_id = '550e8400-e29b-41d4-a716-446655440011' WHERE id = '550e8400-e29b-41d4-a716-446655440005';

-- إدراج السائقين
INSERT INTO drivers (id, code, name_en, name_ar, work_number, personal_number, user_profile_url, username, branch_id, manager_id, user_id, status, license_number, phone, email, hire_date) VALUES
('550e8400-e29b-41d4-a716-446655440020', 501, 'Taha Abdel Fattah Sayed', 'طه عبد الفتاح سيد احمد', '201275777764', '201114089090', 'https://example.com/profile/501', 't.ahmed', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440010', '550e8400-e29b-41d4-a716-446655440013', 'Active', 'DL123456789', '+20 ************', '<EMAIL>', '2023-01-15'),
('550e8400-e29b-41d4-a716-************', 502, 'Ahmed Abdel Aziz', 'احمد عبدالعزيز الصغير', '201276543210', '201114099999', 'https://example.com/profile/502', 'a.abdelaziz', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440011', NULL, 'Active', 'DL987654321', '+20 ************', '<EMAIL>', '2023-03-20'),
('550e8400-e29b-41d4-a716-446655440022', 503, 'Yousef Kamal', 'يوسف كمال', '201278888888', '201115555555', 'https://example.com/profile/503', 'y.kamal', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440011', NULL, 'Active', 'DL456789123', '+20 127 888 8888', '<EMAIL>', '2023-02-10'),
('550e8400-e29b-41d4-a716-446655440023', 504, 'Mahmoud Fathi', 'محمود فتحي', '201279999999', '201116666666', 'https://example.com/profile/504', 'm.fathi', '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440012', NULL, 'Inactive', 'DL789123456', '+20 127 999 9999', '<EMAIL>', '2023-04-05'),
('550e8400-e29b-41d4-a716-446655440024', 505, 'Mona Hassan', 'منى حسن', '201277777777', '201117777777', 'https://example.com/profile/505', 'm.hassan', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440014', 'Active', 'DL321654987', '+20 127 777 7777', '<EMAIL>', '2023-05-15');

-- إدراج المركبات
INSERT INTO vehicles (id, vehicle_id, license_plate, service_type, vehicle_type, make, model, year, color, vin_number, fuel_type, current_km, vehicle_status, branch_id, current_location, driver_id, notes) VALUES
('550e8400-e29b-41d4-a716-446655440030', 'CHPA4916N', '8129 ك ج ق', 'Van', 'Chrysler Pacifica', 'Chrysler', 'Pacifica', 2019, 'Gold', '1C4RC1BG8KC491616', 'Gasoline', 207906, 'Active', '550e8400-e29b-41d4-a716-446655440001', 'Gouna', '550e8400-e29b-41d4-a716-446655440020', 'First van in fleet'),
('550e8400-e29b-41d4-a716-************', '**********', '546 د ى ج', 'London Cab', 'LEVC TX', 'LEVC', 'TX', 2023, 'Black', 'SCBFF7ZA7JC305353', 'Electric', 144047, 'Active', '550e8400-e29b-41d4-a716-446655440003', 'Gouna', '550e8400-e29b-41d4-a716-************', ''),
('550e8400-e29b-41d4-a716-446655440032', 'A393753', '285 د ي ج', 'Bus', 'Mercedes Sprinter', 'Mercedes', 'Sprinter', 2021, 'White', 'WDB9066571N393753', 'Diesel', 117496, 'Active', '550e8400-e29b-41d4-a716-446655440002', 'Alexandria', '550e8400-e29b-41d4-a716-446655440022', ''),
('550e8400-e29b-41d4-a716-************', 'VAN1001', '1234 د ك م', 'Van', 'Toyota Hiace', 'Toyota', 'Hiace', 2020, 'Silver', 'JTFSS22P800100123', 'Diesel', 110000, 'Inactive', '550e8400-e29b-41d4-a716-446655440004', '6th October', NULL, ''),
('550e8400-e29b-41d4-a716-************', 'COMPACT1', '5678 م ن و', 'Compact', 'Hyundai Accent', 'Hyundai', 'Accent', 2022, 'Red', 'KMHCT41BXDU100789', 'Gasoline', 55000, 'Active', '550e8400-e29b-41d4-a716-446655440005', 'Nasr City', '550e8400-e29b-41d4-a716-446655440024', '');

-- إدراج العتبات
INSERT INTO thresholds (id, code, check_type, car_type, parameter, condition_type, value, unit, description, is_active) VALUES
('550e8400-e29b-41d4-a716-446655440040', 'LEVCMaintainance', 'Maintenance', 'LEVC', 'Mileage', 'greater_than', 40000, 'km', 'LEVC maintenance threshold', true),
('550e8400-e29b-41d4-a716-446655440041', 'ComfortMaintainance', 'Maintenance', 'Comfort', 'Mileage', 'greater_than', 10000, 'km', 'Comfort vehicle maintenance threshold', true),
('550e8400-e29b-41d4-a716-446655440042', 'VanTier', 'Tier', 'Van', 'Mileage', 'greater_than', 10000, 'km', 'Van tier threshold', true),
('550e8400-e29b-41d4-a716-446655440043', 'BusTier', 'Tier', 'Bus', 'Mileage', 'greater_than', 40000, 'km', 'Bus tier threshold', true),
('550e8400-e29b-41d4-a716-446655440044', 'CompactTier', 'Tier', 'Compact', 'Mileage', 'greater_than', 50000, 'km', 'Compact car tier threshold', true);

-- إدراج بيانات الصيانة
INSERT INTO maintenance (id, vehicle_id, license_plate, service_type, service_center, description, odometer_reading, parts_cost, labor_cost, total_cost, service_date, next_service_date, status, notes) VALUES
('550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440032', '285 د ي ج', 'Engine Service', 'AutoFix Garage', 'Engine service and oil change', 117496, 54.00, 108.00, 162.00, '2025-03-01', '2025-05-30', 'Completed', 'High-cost maintenance'),
('550e8400-e29b-41d4-a716-446655440051', '550e8400-e29b-41d4-a716-446655440030', '8129 ك ج ق', 'Oil Change', 'QuickLube', 'Regular oil change and filter replacement', 207682, 20.00, 30.00, 50.00, '2025-02-10', '2025-08-10', 'Completed', ''),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '546 د ى ج', 'Tire Replacement', 'TirePro', 'Tire replacement and alignment', 144047, 80.00, 50.00, 130.00, '2025-04-15', '2025-10-15', 'Scheduled', ''),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '1234 د ك م', 'Brake Inspection', 'BrakeMaster', 'Brake pad replacement and brake fluid change', 109000, 35.00, 40.00, 75.00, '2025-01-20', '2025-07-20', 'In Progress', ''),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '5678 م ن و', 'Battery Replacement', 'BatteryPlus', 'Battery replacement and electrical check', 53000, 60.00, 25.00, 85.00, '2025-06-01', '2025-12-01', 'Scheduled', '');

-- إدراج بيانات الوقود
INSERT INTO fuel (id, vehicle_id, license_plate, vehicle_type, vehicle_status, vin, datetime, driver_id, driver_name_en, driver_name_ar, fuel_type, amount, quantity, cost_per_liter, pump_image_reading, difference, odometer, distance, consumption_rate, cost_per_meter, branch_id, vehicle_group, vehicle_model, station) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '546 د ى ج', 'LEVC TX', 'Active', 'SCBFF7ZA7JC305353', '2025-02-28 23:35:01', '550e8400-e29b-41d4-a716-************', 'Ahmed Abdel Aziz', 'احمد عبدالعزيز الصغير', '92', 420.00, 27.54, 15.25, 415.26, 4.74, 144047, 340, 8.1, 1.24, '550e8400-e29b-41d4-a716-446655440003', 'London Cab', 2023, 'Shell Station Downtown'),
('550e8400-e29b-41d4-a716-446655440061', '550e8400-e29b-41d4-a716-446655440030', '8129 ك ج ق', 'Chrysler Pacifica', 'Active', '1C4RC1BG8KC491616', '2025-03-15 09:12:23', '550e8400-e29b-41d4-a716-446655440020', 'Taha Abdel Fattah Sayed', 'طه عبد الفتاح سيد احمد', '95', 350.00, 25.0, 14.00, 208000, 0.00, 207906, 300, 8.3, 1.17, '550e8400-e29b-41d4-a716-446655440001', 'Van', 2019, 'BP Station North'),
('550e8400-e29b-41d4-a716-446655440062', '550e8400-e29b-41d4-a716-446655440032', '285 د ي ج', 'Mercedes Sprinter', 'Active', 'WDB9066571N393753', '2025-04-05 15:30:00', '550e8400-e29b-41d4-a716-446655440022', 'Yousef Kamal', 'يوسف كمال', 'Diesel', 520.00, 40.0, 13.00, 117500, 1.20, 117496, 400, 10.0, 1.30, '550e8400-e29b-41d4-a716-446655440002', 'Bus', 2021, 'Exxon Station West'),
('550e8400-e29b-41d4-a716-446655440063', '550e8400-e29b-41d4-a716-************', '1234 د ك م', 'Toyota Hiace', 'Inactive', 'JTFSS22P800100123', '2025-01-25 13:45:10', NULL, '', '', 'Diesel', 250.00, 18.0, 13.89, 110000, 0.00, 110000, 200, 9.0, 1.25, '550e8400-e29b-41d4-a716-446655440004', 'Van', 2020, 'Total Station'),
('550e8400-e29b-41d4-a716-446655440064', '550e8400-e29b-41d4-a716-************', '5678 م ن و', 'Hyundai Accent', 'Active', 'KMHCT41BXDU100789', '2025-06-10 11:00:00', '550e8400-e29b-41d4-a716-446655440024', 'Mona Hassan', 'منى حسن', '92', 120.00, 10.0, 12.00, 55500, 0.00, 55000, 150, 7.5, 0.80, '550e8400-e29b-41d4-a716-446655440005', 'Compact', 2022, 'Mobil Station');