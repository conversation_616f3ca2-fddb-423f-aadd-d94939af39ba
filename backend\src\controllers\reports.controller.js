const supabase = require('../utils/supabaseClient');

const getDashboardStats = async (req, res) => {
  try {
    // Get all required data in parallel
    const [
      vehiclesResult,
      driversResult,
      branchesResult,
      maintenanceResult,
      fuelResult
    ] = await Promise.all([
      supabase.from('vehicles').select('vehicle_status'),
      supabase.from('drivers').select('status'),
      supabase.from('branches').select('id'),
      supabase.from('maintenance').select('status'),
      supabase.from('fuel').select('quantity').gte('datetime', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
    ]);

    const vehicles = vehiclesResult.data || [];
    const drivers = driversResult.data || [];
    const branches = branchesResult.data || [];
    const maintenance = maintenanceResult.data || [];
    const fuel = fuelResult.data || [];

    const stats = {
      totalVehicles: vehicles.length,
      activeVehicles: vehicles.filter(v => v.vehicle_status === 'Active').length,
      inactiveVehicles: vehicles.filter(v => v.vehicle_status === 'Inactive').length,
      maintenanceVehicles: vehicles.filter(v => v.vehicle_status === 'Maintenance').length,
      
      totalDrivers: drivers.length,
      activeDrivers: drivers.filter(d => d.status === 'Active').length,
      inactiveDrivers: drivers.filter(d => d.status === 'Inactive').length,
      suspendedDrivers: drivers.filter(d => d.status === 'Suspended').length,
      
      totalBranches: branches.length,
      
      totalMaintenance: maintenance.length,
      scheduledMaintenance: maintenance.filter(m => m.status === 'Scheduled').length,
      inProgressMaintenance: maintenance.filter(m => m.status === 'In Progress').length,
      completedMaintenance: maintenance.filter(m => m.status === 'Completed').length,
      
      monthlyFuelConsumption: fuel.reduce((sum, f) => sum + (f.quantity || 0), 0),
      totalFuelRecords: fuel.length
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getVehicleReport = async (req, res) => {
  try {
    const { date_from, date_to, branch_id } = req.query;

    let query = supabase
      .from('vehicles')
      .select(`
        *,
        branch:branches(name),
        driver:drivers(name_en),
        maintenance_count:maintenance(count),
        fuel_records:fuel(quantity, amount, datetime)
      `);

    if (branch_id) {
      query = query.eq('branch_id', branch_id);
    }

    const { data: vehicles, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to generate vehicle report',
        error: error.message
      });
    }

    // Process the data for reporting
    const report = vehicles.map(vehicle => ({
      ...vehicle,
      total_fuel_cost: vehicle.fuel_records?.reduce((sum, f) => sum + (f.amount || 0), 0) || 0,
      total_fuel_quantity: vehicle.fuel_records?.reduce((sum, f) => sum + (f.quantity || 0), 0) || 0,
      maintenance_count: vehicle.maintenance_count?.[0]?.count || 0
    }));

    res.json({
      success: true,
      data: report,
      summary: {
        total_vehicles: vehicles.length,
        total_fuel_cost: report.reduce((sum, v) => sum + v.total_fuel_cost, 0),
        total_fuel_quantity: report.reduce((sum, v) => sum + v.total_fuel_quantity, 0),
        average_fuel_cost_per_vehicle: vehicles.length > 0 
          ? report.reduce((sum, v) => sum + v.total_fuel_cost, 0) / vehicles.length 
          : 0
      }
    });
  } catch (error) {
    console.error('Get vehicle report error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getMaintenanceReport = async (req, res) => {
  try {
    const { date_from, date_to, vehicle_id, status } = req.query;

    let query = supabase
      .from('maintenance')
      .select(`
        *,
        vehicle:vehicles(license_plate, make, model, branch:branches(name))
      `);

    if (date_from) {
      query = query.gte('service_date', date_from);
    }

    if (date_to) {
      query = query.lte('service_date', date_to);
    }

    if (vehicle_id) {
      query = query.eq('vehicle_id', vehicle_id);
    }

    if (status) {
      query = query.eq('status', status);
    }

    query = query.order('service_date', { ascending: false });

    const { data: maintenance, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to generate maintenance report',
        error: error.message
      });
    }

    const summary = {
      total_records: maintenance.length,
      total_cost: maintenance.reduce((sum, m) => sum + (m.total_cost || 0), 0),
      average_cost: maintenance.length > 0 
        ? maintenance.reduce((sum, m) => sum + (m.total_cost || 0), 0) / maintenance.length 
        : 0,
      by_status: maintenance.reduce((acc, m) => {
        acc[m.status] = (acc[m.status] || 0) + 1;
        return acc;
      }, {}),
      by_service_type: maintenance.reduce((acc, m) => {
        acc[m.service_type] = (acc[m.service_type] || 0) + 1;
        return acc;
      }, {})
    };

    res.json({
      success: true,
      data: maintenance,
      summary
    });
  } catch (error) {
    console.error('Get maintenance report error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getFuelReport = async (req, res) => {
  try {
    const { date_from, date_to, vehicle_id, driver_id } = req.query;

    let query = supabase
      .from('fuel')
      .select(`
        *,
        vehicle:vehicles(license_plate, make, model, branch:branches(name)),
        driver:drivers(name_en, name_ar)
      `);

    if (date_from) {
      query = query.gte('datetime', date_from);
    }

    if (date_to) {
      query = query.lte('datetime', date_to);
    }

    if (vehicle_id) {
      query = query.eq('vehicle_id', vehicle_id);
    }

    if (driver_id) {
      query = query.eq('driver_id', driver_id);
    }

    query = query.order('datetime', { ascending: false });

    const { data: fuel, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to generate fuel report',
        error: error.message
      });
    }

    const summary = {
      total_records: fuel.length,
      total_amount: fuel.reduce((sum, f) => sum + (f.amount || 0), 0),
      total_quantity: fuel.reduce((sum, f) => sum + (f.quantity || 0), 0),
      average_cost_per_liter: fuel.length > 0 
        ? fuel.reduce((sum, f) => sum + (f.amount || 0), 0) / 
          fuel.reduce((sum, f) => sum + (f.quantity || 0), 0)
        : 0,
      by_fuel_type: fuel.reduce((acc, f) => {
        if (!acc[f.fuel_type]) {
          acc[f.fuel_type] = { count: 0, amount: 0, quantity: 0 };
        }
        acc[f.fuel_type].count++;
        acc[f.fuel_type].amount += f.amount || 0;
        acc[f.fuel_type].quantity += f.quantity || 0;
        return acc;
      }, {}),
      by_vehicle: fuel.reduce((acc, f) => {
        const vehicleKey = f.vehicle?.license_plate || 'Unknown';
        if (!acc[vehicleKey]) {
          acc[vehicleKey] = { count: 0, amount: 0, quantity: 0 };
        }
        acc[vehicleKey].count++;
        acc[vehicleKey].amount += f.amount || 0;
        acc[vehicleKey].quantity += f.quantity || 0;
        return acc;
      }, {})
    };

    res.json({
      success: true,
      data: fuel,
      summary
    });
  } catch (error) {
    console.error('Get fuel report error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getDriverReport = async (req, res) => {
  try {
    const { branch_id, status } = req.query;

    let query = supabase
      .from('drivers')
      .select(`
        *,
        branch:branches(name),
        assigned_vehicle:vehicles(license_plate, make, model),
        fuel_records:fuel(quantity, amount, datetime)
      `);

    if (branch_id) {
      query = query.eq('branch_id', branch_id);
    }

    if (status) {
      query = query.eq('status', status);
    }

    const { data: drivers, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to generate driver report',
        error: error.message
      });
    }

    // Process the data for reporting
    const report = drivers.map(driver => ({
      ...driver,
      total_fuel_records: driver.fuel_records?.length || 0,
      total_fuel_cost: driver.fuel_records?.reduce((sum, f) => sum + (f.amount || 0), 0) || 0,
      total_fuel_quantity: driver.fuel_records?.reduce((sum, f) => sum + (f.quantity || 0), 0) || 0
    }));

    res.json({
      success: true,
      data: report,
      summary: {
        total_drivers: drivers.length,
        active_drivers: drivers.filter(d => d.status === 'Active').length,
        inactive_drivers: drivers.filter(d => d.status === 'Inactive').length,
        suspended_drivers: drivers.filter(d => d.status === 'Suspended').length
      }
    });
  } catch (error) {
    console.error('Get driver report error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getDashboardStats,
  getVehicleReport,
  getMaintenanceReport,
  getFuelReport,
  getDriverReport
};