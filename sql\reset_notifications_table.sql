-- Reset Notifications Table Script for Supabase
-- This script will completely remove and recreate the notifications table
-- Run this in Supabase SQL Editor

-- ========================================
-- STEP 1: DROP EXISTING TABLE AND DEPENDENCIES
-- ========================================

-- Drop existing policies first
DROP POLICY IF EXISTS "Users can manage their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can insert their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can delete their own notifications" ON public.notifications;

-- Drop existing indexes
DROP INDEX IF EXISTS idx_notifications_user_id;
DROP INDEX IF EXISTS idx_notifications_created_at;
DROP INDEX IF EXISTS idx_notifications_read;
DROP INDEX IF EXISTS idx_notifications_type;
DROP INDEX IF EXISTS idx_notifications_severity;

-- Drop existing functions
DROP FUNCTION IF EXISTS update_notifications_updated_at();
DROP FUNCTION IF EXISTS create_system_notification(TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS broadcast_notification(TEXT, TEXT, TEXT, TEXT);

-- Drop existing triggers
DROP TRIGGER IF EXISTS trigger_update_notifications_updated_at ON public.notifications;

-- Drop the table completely
DROP TABLE IF EXISTS public.notifications CASCADE;

-- ========================================
-- STEP 2: CREATE NEW NOTIFICATIONS TABLE
-- ========================================

-- Create notifications table with proper structure
CREATE TABLE public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL CHECK (length(title) > 0 AND length(title) <= 255),
    message TEXT NOT NULL CHECK (length(message) > 0 AND length(message) <= 1000),
    type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'urgent')),
    read BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMPTZ DEFAULT NULL,
    metadata JSONB DEFAULT '{}' NOT NULL
);

-- ========================================
-- STEP 3: CREATE INDEXES FOR PERFORMANCE
-- ========================================

-- Primary indexes for common queries
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at DESC);
CREATE INDEX idx_notifications_read ON public.notifications(read);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_severity ON public.notifications(severity);

-- Composite indexes for complex queries
CREATE INDEX idx_notifications_user_read ON public.notifications(user_id, read);
CREATE INDEX idx_notifications_user_created ON public.notifications(user_id, created_at DESC);
CREATE INDEX idx_notifications_user_type ON public.notifications(user_id, type);

-- ========================================
-- STEP 4: ENABLE ROW LEVEL SECURITY
-- ========================================

-- Enable RLS
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create comprehensive RLS policies
CREATE POLICY "Users can view their own notifications" 
ON public.notifications FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notifications" 
ON public.notifications FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" 
ON public.notifications FOR UPDATE 
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notifications" 
ON public.notifications FOR DELETE 
USING (auth.uid() = user_id);

-- ========================================
-- STEP 5: CREATE UTILITY FUNCTIONS
-- ========================================

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER trigger_update_notifications_updated_at
    BEFORE UPDATE ON public.notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_notifications_updated_at();

-- Function to create system notifications
CREATE OR REPLACE FUNCTION create_system_notification(
    p_user_id UUID,
    p_title TEXT,
    p_message TEXT,
    p_type TEXT DEFAULT 'info',
    p_severity TEXT DEFAULT 'medium'
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (user_id, title, message, type, severity)
    VALUES (p_user_id, p_title, p_message, p_type, p_severity)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to broadcast notification to all users
CREATE OR REPLACE FUNCTION broadcast_notification(
    p_title TEXT,
    p_message TEXT,
    p_type TEXT DEFAULT 'info',
    p_severity TEXT DEFAULT 'medium'
)
RETURNS INTEGER AS $$
DECLARE
    user_record RECORD;
    notification_count INTEGER := 0;
BEGIN
    FOR user_record IN SELECT id FROM auth.users LOOP
        INSERT INTO public.notifications (user_id, title, message, type, severity)
        VALUES (user_record.id, p_title, p_message, p_type, p_severity);
        notification_count := notification_count + 1;
    END LOOP;
    
    RETURN notification_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- STEP 6: INSERT SAMPLE DATA
-- ========================================

-- Insert sample notifications for the current user (if authenticated)
DO $$
DECLARE
    current_user_id UUID;
BEGIN
    -- Get current user ID
    current_user_id := auth.uid();
    
    -- Only insert if user is authenticated
    IF current_user_id IS NOT NULL THEN
        INSERT INTO public.notifications (user_id, title, message, type, severity, read) 
        VALUES 
            (current_user_id, 'Welcome to Fleet Management System', 'Your account has been successfully created! You can now manage your fleet efficiently with all available features.', 'success', 'low', false),
            (current_user_id, 'System Maintenance Scheduled', 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM. Some features may be temporarily unavailable during this time.', 'warning', 'medium', false),
            (current_user_id, 'Vehicle Maintenance Alert', 'Vehicle ABC-123 is due for maintenance. Please schedule an appointment as soon as possible to avoid any issues.', 'error', 'high', false),
            (current_user_id, 'New Feature Available', 'We have added new reporting features to help you track your fleet performance better.', 'info', 'low', true),
            (current_user_id, 'License Expiry Warning', 'Driver license for John Doe will expire in 30 days. Please renew it before expiration.', 'warning', 'high', false)
        ON CONFLICT DO NOTHING;
        
        RAISE NOTICE 'Sample notifications created for user: %', current_user_id;
    ELSE
        RAISE NOTICE 'No authenticated user found. Sample data not inserted.';
    END IF;
END $$;

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Verify table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'notifications' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Verify indexes
SELECT 
    indexname, 
    indexdef 
FROM pg_indexes 
WHERE tablename = 'notifications' 
AND schemaname = 'public';

-- Verify policies
SELECT 
    policyname, 
    permissive, 
    roles, 
    cmd, 
    qual, 
    with_check 
FROM pg_policies 
WHERE tablename = 'notifications' 
AND schemaname = 'public';

-- Count sample data
SELECT 
    COUNT(*) as total_notifications,
    COUNT(CASE WHEN read = false THEN 1 END) as unread_count,
    COUNT(CASE WHEN read = true THEN 1 END) as read_count
FROM public.notifications;

-- Show sample data
SELECT 
    id,
    title,
    type,
    severity,
    read,
    created_at
FROM public.notifications 
ORDER BY created_at DESC 
LIMIT 5;

RAISE NOTICE 'Notifications table has been successfully reset and recreated!';
RAISE NOTICE 'You can now use the notifications system with full database integration.';
