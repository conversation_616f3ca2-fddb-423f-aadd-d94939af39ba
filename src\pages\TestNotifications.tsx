import React from 'react';
import { Bell, AlertCircle, CheckCircle, AlertTriangle, Info } from 'lucide-react';

const TestNotifications: React.FC = () => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  // Simulate loading for 2 seconds then show demo data
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
      console.log('✅ Test notifications loaded');
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const demoNotifications = [
    {
      id: 'demo-1',
      title: 'Welcome to Fleet Management',
      message: 'Your account has been successfully created! You can now manage your fleet efficiently.',
      type: 'success' as const,
      severity: 'low' as const,
      read: false,
      created_at: new Date().toISOString()
    },
    {
      id: 'demo-2',
      title: 'System Maintenance Scheduled',
      message: 'Scheduled maintenance will occur tonight from 2-4 AM. Some features may be temporarily unavailable.',
      type: 'warning' as const,
      severity: 'medium' as const,
      read: false,
      created_at: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 'demo-3',
      title: 'Vehicle Maintenance Due',
      message: 'Vehicle ABC-123 is due for maintenance. Please schedule an appointment.',
      type: 'info' as const,
      severity: 'high' as const,
      read: true,
      created_at: new Date(Date.now() - 7200000).toISOString()
    }
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading test notifications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
          <Bell className="h-8 w-8 mr-3" />
          Test Notifications
        </h1>
        <p className="text-gray-600 mt-1">
          Simple test page to verify notifications display correctly
        </p>
      </div>

      {/* Demo Mode Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-center">
          <Info className="h-5 w-5 text-yellow-600 mr-2" />
          <div>
            <p className="text-yellow-800 font-medium">Test Mode Active</p>
            <p className="text-yellow-700 text-sm">
              This is a simple test page showing demo notifications.
            </p>
          </div>
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="divide-y divide-gray-200">
          {demoNotifications.map((notification) => (
            <div
              key={notification.id}
              className={`p-6 hover:bg-gray-50 transition-colors ${
                !notification.read ? 'border-l-4 border-l-blue-500' : ''
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 mt-1">
                  {getNotificationIcon(notification.type)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">
                      {notification.title}
                    </h3>
                    {!notification.read && (
                      <span className="flex h-2 w-2">
                        <span className="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-blue-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                      </span>
                    )}
                  </div>

                  <p className="mt-2 text-gray-600">
                    {notification.message}
                  </p>

                  {/* Metadata */}
                  <div className="mt-3 flex items-center space-x-3">
                    <span className="text-sm text-gray-500">
                      {formatTimeAgo(notification.created_at)}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full border ${
                      notification.severity === 'urgent' ? 'bg-red-100 text-red-800 border-red-200' :
                      notification.severity === 'high' ? 'bg-orange-100 text-orange-800 border-orange-200' :
                      notification.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                      'bg-blue-100 text-blue-800 border-blue-200'
                    }`}>
                      {notification.severity}
                    </span>
                    <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700 border border-gray-200">
                      {notification.type}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestNotifications;
