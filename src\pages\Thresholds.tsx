import React, { useState, useEffect } from 'react';
import { Settings, AlertTriangle, Save, Plus } from 'lucide-react';
import RoleGuard from '../components/RoleGuard';

interface Threshold {
  id: number;
  type: string;
  parameter: string;
  condition: string;
  value: number;
  unit: string;
  description: string;
  is_active: boolean;
}

interface ThresholdFormData {
  type: string;
  parameter: string;
  condition: string;
  value: number;
  unit: string;
  description: string;
  is_active: boolean;
}

const Thresholds: React.FC = () => {
  return (
    <RoleGuard requiredRole={['Super Admin', 'Admin']}>
      <ThresholdsContent />
    </RoleGuard>
  );
};

const ThresholdsContent: React.FC = () => {
  const [thresholds, setThresholds] = useState<Threshold[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingThreshold, setEditingThreshold] = useState<Threshold | null>(null);
  const [formData, setFormData] = useState<ThresholdFormData>({
    type: 'Maintenance',
    parameter: 'Mileage',
    condition: 'greater_than',
    value: 0,
    unit: 'km',
    description: '',
    is_active: true
  });

  useEffect(() => {
    fetchThresholds();
  }, []);

  const fetchThresholds = async () => {
    try {
      // Mock data for demo
      setThresholds([
        {
          id: 1,
          type: 'Maintenance',
          parameter: 'Mileage',
          condition: 'greater_than',
          value: 50000,
          unit: 'km',
          description: 'Alert when vehicle mileage exceeds 50,000 km',
          is_active: true
        },
        {
          id: 2,
          type: 'Maintenance',
          parameter: 'Last Service',
          condition: 'days_since',
          value: 90,
          unit: 'days',
          description: 'Alert when last service was more than 90 days ago',
          is_active: true
        },
        {
          id: 3,
          type: 'Fuel',
          parameter: 'Daily Consumption',
          condition: 'greater_than',
          value: 100,
          unit: 'liters',
          description: 'Alert when daily fuel consumption exceeds 100 liters',
          is_active: true
        },
        {
          id: 4,
          type: 'Driver',
          parameter: 'License Expiry',
          condition: 'days_until',
          value: 30,
          unit: 'days',
          description: 'Alert when driver license expires within 30 days',
          is_active: true
        },
        {
          id: 5,
          type: 'Vehicle',
          parameter: 'Insurance Expiry',
          condition: 'days_until',
          value: 30,
          unit: 'days',
          description: 'Alert when vehicle insurance expires within 30 days',
          is_active: true
        }
      ]);
    } catch (error) {
      console.error('Failed to fetch thresholds:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddThreshold = () => {
    setEditingThreshold(null);
    setFormData({
      type: 'Maintenance',
      parameter: 'Mileage',
      condition: 'greater_than',
      value: 0,
      unit: 'km',
      description: '',
      is_active: true
    });
    setShowModal(true);
  };

  const handleEditThreshold = (threshold: Threshold) => {
    setEditingThreshold(threshold);
    setFormData({
      type: threshold.type,
      parameter: threshold.parameter,
      condition: threshold.condition,
      value: threshold.value,
      unit: threshold.unit,
      description: threshold.description,
      is_active: threshold.is_active
    });
    setShowModal(true);
  };

  const handleSaveThreshold = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    try {
      if (editingThreshold) {
        // Update existing threshold
        const updatedThreshold = { ...editingThreshold, ...formData };
        setThresholds(prev => prev.map(t => t.id === editingThreshold.id ? updatedThreshold : t));
      } else {
        // Add new threshold
        const newThreshold: Threshold = {
          id: Date.now(),
          ...formData
        };
        setThresholds(prev => [...prev, newThreshold]);
      }
      
      setShowModal(false);
      setEditingThreshold(null);
    } catch (error) {
      console.error('Failed to save threshold:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleToggleActive = (id: number) => {
    setThresholds(prev => 
      prev.map(threshold => 
        threshold.id === id 
          ? { ...threshold, is_active: !threshold.is_active }
          : threshold
      )
    );
  };

  const handleValueChange = (id: number, newValue: number) => {
    setThresholds(prev => 
      prev.map(threshold => 
        threshold.id === id 
          ? { ...threshold, value: newValue }
          : threshold
      )
    );
  };

  const handleSaveChanges = async () => {
    setSaving(true);
    try {
      // API call to save thresholds
      await new Promise(resolve => setTimeout(resolve, 1000)); // Mock delay
      // Show success message
    } catch (error) {
      console.error('Failed to save thresholds:', error);
    } finally {
      setSaving(false);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'maintenance':
        return 'bg-orange-100 text-orange-800';
      case 'fuel':
        return 'bg-blue-100 text-blue-800';
      case 'driver':
        return 'bg-green-100 text-green-800';
      case 'vehicle':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConditionText = (condition: string) => {
    switch (condition) {
      case 'greater_than':
        return 'is greater than';
      case 'less_than':
        return 'is less than';
      case 'days_since':
        return 'days since';
      case 'days_until':
        return 'days until';
      default:
        return condition;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Alert Thresholds</h1>
          <p className="text-gray-600">Configure automated alerts and notifications for your fleet</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={handleAddThreshold}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Add Threshold</span>
          </button>
          <button
            onClick={handleSaveChanges}
            disabled={saving}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 disabled:opacity-50 transition-colors"
          >
            <Save className="h-4 w-4" />
            <span>{saving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </div>

      {/* Thresholds List */}
      <div className="space-y-4">
        {thresholds.map((threshold) => (
          <div key={threshold.id} className="bg-white rounded-lg shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                    <Settings className="h-6 w-6 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{threshold.parameter}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(threshold.type)}`}>
                        {threshold.type}
                      </span>
                      {threshold.is_active && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      )}
                    </div>
                    <p className="text-gray-600 mb-3">{threshold.description}</p>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">Alert when</span>
                        <span className="text-sm font-medium text-gray-900">{threshold.parameter}</span>
                        <span className="text-sm text-gray-500">{getConditionText(threshold.condition)}</span>
                        <input
                          type="number"
                          value={threshold.value}
                          onChange={(e) => handleValueChange(threshold.id, parseInt(e.target.value))}
                          className="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <span className="text-sm text-gray-500">{threshold.unit}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleEditThreshold(threshold)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
                  >
                    Edit
                  </button>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={threshold.is_active}
                      onChange={() => handleToggleActive(threshold.id)}
                      className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <span className="ml-2 text-sm text-gray-700">Active</span>
                  </label>
                </div>
              </div>

              {threshold.type === 'Maintenance' && (
                <div className="border-t border-gray-200 pt-4">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                      <span className="text-sm text-yellow-800">
                        This threshold affects {threshold.type.toLowerCase()} scheduling and notifications.
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Threshold Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {thresholds.filter(t => t.is_active).length}
            </div>
            <div className="text-sm text-gray-500">Active Thresholds</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {thresholds.filter(t => t.type === 'Maintenance').length}
            </div>
            <div className="text-sm text-gray-500">Maintenance Alerts</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {thresholds.filter(t => t.type === 'Driver').length}
            </div>
            <div className="text-sm text-gray-500">Driver Alerts</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {thresholds.filter(t => t.type === 'Vehicle').length}
            </div>
            <div className="text-sm text-gray-500">Vehicle Alerts</div>
          </div>
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {editingThreshold ? 'Edit Threshold' : 'Add New Threshold'}
            </h2>
            
            <form onSubmit={handleSaveThreshold} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="Maintenance">Maintenance</option>
                  <option value="Fuel">Fuel</option>
                  <option value="Driver">Driver</option>
                  <option value="Vehicle">Vehicle</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Parameter
                </label>
                <input
                  type="text"
                  value={formData.parameter}
                  onChange={(e) => setFormData(prev => ({ ...prev, parameter: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Condition
                </label>
                <select
                  value={formData.condition}
                  onChange={(e) => setFormData(prev => ({ ...prev, condition: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="greater_than">Greater Than</option>
                  <option value="less_than">Less Than</option>
                  <option value="days_since">Days Since</option>
                  <option value="days_until">Days Until</option>
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Value
                  </label>
                  <input
                    type="number"
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: parseInt(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Unit
                  </label>
                  <input
                    type="text"
                    value={formData.unit}
                    onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  required
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700">Active</span>
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {saving ? 'Saving...' : (editingThreshold ? 'Update' : 'Add')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Thresholds;