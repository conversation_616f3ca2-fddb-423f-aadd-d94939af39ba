# إعادة ضبط جدول الإشعارات في Supabase

## 📋 الخطوات المطلوبة:

### 1. 🔐 تسجيل الدخول إلى Supabase
- اذهب إلى [Supabase Dashboard](https://app.supabase.com)
- سجل دخول إلى مشروعك

### 2. 🛠️ فتح SQL Editor
- من القائمة الجانبية، اختر **SQL Editor**
- انقر على **New Query**

### 3. 🗑️ حذف وإعادة إنشاء الجدول

#### الخيار الأول: إعادة ضبط شاملة (مُوصى به)
```sql
-- انسخ والصق محتوى الملف التالي:
sql/reset_notifications_table.sql
```

#### الخيار الثاني: إعادة ضبط سريعة
```sql
-- انسخ والصق محتوى الملف التالي:
sql/quick_reset_notifications.sql
```

### 4. ▶️ تشغيل الكود
- انقر على **Run** أو اضغط `Ctrl + Enter`
- انتظر حتى يكتمل التنفيذ

### 5. ✅ التحقق من النجاح
يجب أن ترى رسائل تأكيد مثل:
- `Notifications table has been successfully reset`
- `notification_count: 5` (أو عدد الإشعارات المُدرجة)

## 🔍 التحقق من البيانات

### عرض جميع الإشعارات:
```sql
SELECT * FROM public.notifications ORDER BY created_at DESC;
```

### عدد الإشعارات:
```sql
SELECT COUNT(*) FROM public.notifications;
```

### التحقق من الفهارس:
```sql
SELECT indexname FROM pg_indexes WHERE tablename = 'notifications';
```

### التحقق من السياسات:
```sql
SELECT policyname FROM pg_policies WHERE tablename = 'notifications';
```

## 🚨 ملاحظات مهمة:

1. **النسخ الاحتياطي**: هذا الكود سيحذف جميع الإشعارات الموجودة
2. **المصادقة**: تأكد من تسجيل دخولك في Supabase قبل تشغيل الكود
3. **البيانات التجريبية**: سيتم إدراج 5 إشعارات تجريبية تلقائياً
4. **الأمان**: Row Level Security مُفعل لحماية البيانات

## 🔧 ما يتم إنشاؤه:

### الجدول:
- `public.notifications` مع جميع الحقول المطلوبة
- قيود البيانات (constraints) للتحقق من صحة البيانات
- مراجع خارجية (foreign keys) للمستخدمين

### الفهارس:
- `idx_notifications_user_id`
- `idx_notifications_created_at`
- `idx_notifications_read`
- وفهارس أخرى للأداء

### السياسات الأمنية:
- سياسات RLS لحماية بيانات كل مستخدم
- صلاحيات القراءة والكتابة والتحديث والحذف

### الوظائف المساعدة:
- `update_notifications_updated_at()` - تحديث تلقائي للوقت
- `create_system_notification()` - إنشاء إشعارات النظام
- `broadcast_notification()` - إرسال إشعار لجميع المستخدمين

## 🎯 بعد التنفيذ:

1. **اختبر التطبيق**: اذهب إلى صفحة الإشعارات في التطبيق
2. **تحقق من البيانات**: يجب أن ترى الإشعارات التجريبية
3. **اختبر العمليات**: جرب إنشاء، قراءة، تحديث، وحذف الإشعارات
4. **تحقق من الأداء**: يجب أن تكون الصفحة سريعة ومستقرة

## 🆘 في حالة المشاكل:

### إذا فشل التنفيذ:
1. تحقق من رسائل الخطأ في SQL Editor
2. تأكد من تسجيل دخولك
3. جرب الخيار السريع `quick_reset_notifications.sql`

### إذا لم تظهر البيانات:
1. تحقق من RLS policies
2. تأكد من تسجيل دخولك في التطبيق
3. تحقق من user_id في الجدول

### للمساعدة الإضافية:
- راجع Supabase Logs في Dashboard
- تحقق من Network tab في Developer Tools
- راجع Console للأخطاء JavaScript
