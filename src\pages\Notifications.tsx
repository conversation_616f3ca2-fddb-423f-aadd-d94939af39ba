import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Filter, 
  Search, 
  Plus,
  AlertT<PERSON>gle, 
  Info, 
  AlertCircle, 
  CheckCircle,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react';
import { useNotifications } from '../hooks/useNotifications';
import { useNotificationCount } from '../hooks/useNotificationCount';
import { Notification } from '../types';
import CreateNotificationModal from '../components/CreateNotificationModal';

const NotificationsPage: React.FC = () => {
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<{
    type?: string;
    severity?: string;
    read?: boolean;
  }>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const {
    data: notifications,
    loading,
    error,
    count,
    hasM<PERSON>,
    lastUpdated,
    markAsRead,
    deleteNotification,
    mark<PERSON>ll<PERSON>R<PERSON>,
    markMultipleAsRead,
    deleteMultiple,
    createNotification
  } = useNotifications({
    realTime: false, // Disable real-time to prevent infinite loading
    autoRefresh: false, // Disable auto-refresh to prevent infinite loading
    refreshInterval: 0, // No auto-refresh
    filters,
    pagination: { page: currentPage, limit: 20 }
  });

  // Debug logging
  React.useEffect(() => {
    console.log('🔔 Notifications Page Debug:', {
      loading,
      error,
      notificationsCount: notifications?.length || 0,
      count,
      hasMore,
      lastUpdated
    });
  }, [loading, error, notifications, count, hasMore, lastUpdated]);

  // Loading timeout - if loading for more than 5 seconds, force stop loading
  const [loadingTimeout, setLoadingTimeout] = React.useState(false);
  const [forceShowContent, setForceShowContent] = React.useState(false);

  React.useEffect(() => {
    if (loading && !error) {
      const timer = setTimeout(() => {
        setLoadingTimeout(true);
        setForceShowContent(true);
        console.warn('🔔 Notifications loading timeout after 3 seconds - showing demo content');
      }, 3000); // Reduced to 3 seconds to match API timeout
      return () => clearTimeout(timer);
    } else {
      setLoadingTimeout(false);
      if (!loading) {
        setForceShowContent(false);
      }
    }
  }, [loading, error]);

  // Force show content after initial mount to prevent infinite loading
  React.useEffect(() => {
    const forceTimer = setTimeout(() => {
      if (loading) {
        console.warn('🔔 Force showing content after 2 seconds to prevent infinite loading');
        setForceShowContent(true);
      }
    }, 2000);
    return () => clearTimeout(forceTimer);
  }, []);

  const { data: unreadCount } = useNotificationCount({ realTime: false }); // Disable real-time for count too

  // Demo notifications fallback
  const demoNotifications = React.useMemo(() => [
    {
      id: 'demo-1',
      user_id: 'demo-user',
      title: 'Welcome to Fleet Management',
      message: 'Your account has been successfully created! You can now manage your fleet efficiently.',
      type: 'success' as const,
      severity: 'low' as const,
      read: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'demo-2',
      user_id: 'demo-user',
      title: 'System Maintenance Scheduled',
      message: 'Scheduled maintenance will occur tonight from 2-4 AM. Some features may be temporarily unavailable.',
      type: 'warning' as const,
      severity: 'medium' as const,
      read: false,
      created_at: new Date(Date.now() - 3600000).toISOString(),
      updated_at: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 'demo-3',
      user_id: 'demo-user',
      title: 'Vehicle Maintenance Due',
      message: 'Vehicle ABC-123 is due for maintenance. Please schedule an appointment.',
      type: 'info' as const,
      severity: 'high' as const,
      read: true,
      created_at: new Date(Date.now() - 7200000).toISOString(),
      updated_at: new Date(Date.now() - 7200000).toISOString()
    }
  ], []);

  // Use demo notifications if we have errors, no data, or force show content
  const displayNotifications = React.useMemo(() => {
    // Always show demo notifications if there's an error or timeout
    if (error || forceShowContent || loadingTimeout) {
      console.log('🔔 Using demo notifications due to:', { error: !!error, forceShowContent, loadingTimeout });
      return demoNotifications;
    }
    // If we have real notifications, use them
    if (notifications && notifications.length > 0) {
      return notifications;
    }
    // If loading is false and we have no notifications, show demo
    if (!loading) {
      console.log('🔔 No real notifications found, using demo notifications');
      return demoNotifications;
    }
    // While loading, return empty array (will show loading spinner)
    return [];
  }, [notifications, error, forceShowContent, loadingTimeout, loading, demoNotifications]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getSeverityColor = (severity: Notification['severity']) => {
    switch (severity) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const filteredNotifications = displayNotifications?.filter(notification =>
    notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    notification.message.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const toggleNotificationSelection = (id: string) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(nId => nId !== id)
        : [...prev, id]
    );
  };

  const selectAllNotifications = () => {
    const allIds = filteredNotifications.map(n => n.id);
    setSelectedNotifications(allIds);
  };

  const clearSelection = () => {
    setSelectedNotifications([]);
  };

  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.length > 0) {
      await markMultipleAsRead(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedNotifications.length > 0) {
      await deleteMultiple(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const handleCreateNotification = async (notificationData: Omit<Notification, 'id' | 'created_at' | 'updated_at'>) => {
    await createNotification(notificationData);
    setShowCreateModal(false);
  };

  const applyFilter = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value
    }));
    setCurrentPage(1);
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Bell className="h-8 w-8 mr-3" />
              Notifications
              {unreadCount > 0 && (
                <span className="ml-3 bg-red-500 text-white text-sm px-2 py-1 rounded-full">
                  {unreadCount} unread
                </span>
              )}
            </h1>
            <p className="text-gray-600 mt-1">
              Manage your notifications and stay updated with important alerts
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Notification</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search notifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-3">
            <select
              value={filters.type || 'all'}
              onChange={(e) => applyFilter('type', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
            >
              <option value="all">All Types</option>
              <option value="info">Info</option>
              <option value="success">Success</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
            </select>
            <select
              value={filters.severity || 'all'}
              onChange={(e) => applyFilter('severity', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
            >
              <option value="all">All Priorities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
            <select
              value={filters.read === undefined ? 'all' : filters.read ? 'read' : 'unread'}
              onChange={(e) => applyFilter('read', e.target.value === 'all' ? undefined : e.target.value === 'read')}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
            >
              <option value="all">All Status</option>
              <option value="unread">Unread</option>
              <option value="read">Read</option>
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedNotifications.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
            <span className="text-blue-700 font-medium">
              {selectedNotifications.length} notification(s) selected
            </span>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleBulkMarkAsRead}
                className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
              >
                <Check className="h-4 w-4" />
                <span>Mark as Read</span>
              </button>
              <button
                onClick={handleBulkDelete}
                className="text-red-600 hover:text-red-800 flex items-center space-x-1"
              >
                <Trash2 className="h-4 w-4" />
                <span>Delete</span>
              </button>
              <button
                onClick={clearSelection}
                className="text-gray-600 hover:text-gray-800"
              >
                Clear Selection
              </button>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-4 flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            {filteredNotifications.length > 0 && (
              <button
                onClick={selectedNotifications.length === filteredNotifications.length ? clearSelection : selectAllNotifications}
                className="text-blue-600 hover:text-blue-800"
              >
                {selectedNotifications.length === filteredNotifications.length ? 'Deselect All' : 'Select All'}
              </button>
            )}
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
              >
                <CheckCheck className="h-4 w-4" />
                <span>Mark All as Read</span>
              </button>
            )}
          </div>
          {lastUpdated && (
            <span className="text-gray-500">
              Last updated: {formatTimeAgo(lastUpdated.toISOString())}
            </span>
          )}
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <Bell className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-2xl font-bold text-gray-900">{count}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <EyeOff className="h-8 w-8 text-red-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Unread</p>
              <p className="text-2xl font-bold text-gray-900">{unreadCount}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Read</p>
              <p className="text-2xl font-bold text-gray-900">{count - unreadCount}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-orange-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Urgent</p>
              <p className="text-2xl font-bold text-gray-900">
                {notifications?.filter(n => n.severity === 'urgent').length || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading && !loadingTimeout && !forceShowContent && !error ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading notifications...</p>
          </div>
        ) : error && !forceShowContent && filteredNotifications.length === 0 ? (
          <div className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <p className="text-yellow-600 font-medium">
              Database Connection Issue
            </p>
            <p className="text-gray-600 mt-1">
              Using demo notifications due to connectivity issues. The system is working in offline mode.
            </p>
            <div className="mt-4 text-sm text-gray-500">
              Demo notifications are being displayed below.
            </div>
          </div>
        ) : filteredNotifications.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-6 hover:bg-gray-50 transition-colors ${
                  selectedNotifications.includes(notification.id) ? 'bg-blue-50' : ''
                } ${!notification.read ? 'border-l-4 border-l-blue-500' : ''}`}
              >
                <div className="flex items-start space-x-4">
                  {/* Selection Checkbox */}
                  <input
                    type="checkbox"
                    checked={selectedNotifications.includes(notification.id)}
                    onChange={() => toggleNotificationSelection(notification.id)}
                    className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />

                  {/* Notification Icon */}
                  <div className="flex-shrink-0">
                    {getNotificationIcon(notification.type)}
                  </div>

                  {/* Notification Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className={`text-lg font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-600'}`}>
                          {notification.title}
                          {!notification.read && (
                            <span className="ml-2 inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                          )}
                        </h3>
                        <p className={`mt-1 ${!notification.read ? 'text-gray-700' : 'text-gray-500'}`}>
                          {notification.message}
                        </p>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2 ml-4">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="text-blue-600 hover:text-blue-800 p-1"
                            title="Mark as read"
                          >
                            <Check className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notification.id)}
                          className="text-gray-400 hover:text-red-600 p-1"
                          title="Delete notification"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    {/* Metadata */}
                    <div className="mt-3 flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-sm text-gray-500">
                          {formatTimeAgo(notification.created_at)}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full border ${getSeverityColor(notification.severity)}`}>
                          {notification.severity}
                        </span>
                        <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700 border border-gray-200">
                          {notification.type}
                        </span>
                      </div>

                      {notification.action_url && notification.action_text && (
                        <a
                          href={notification.action_url}
                          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {notification.action_text}
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-12 text-center">
            <Bell className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
            <p className="text-gray-600">
              {searchTerm || Object.keys(filters).length > 0
                ? "Try adjusting your search or filters"
                : "You're all caught up! No notifications to display."}
            </p>
          </div>
        )}

        {/* Pagination */}
        {hasMore && (
          <div className="p-4 border-t border-gray-200 text-center">
            <button
              onClick={() => setCurrentPage(prev => prev + 1)}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              Load More
            </button>
          </div>
        )}
      </div>

      {/* Create Notification Modal */}
      <CreateNotificationModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateNotification}
      />
    </div>
  );
};

export default NotificationsPage;
