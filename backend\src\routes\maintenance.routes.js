const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth.middleware');
const {
  getAllMaintenance,
  getMaintenanceById,
  createMaintenance,
  updateMaintenance,
  deleteMaintenance,
  getMaintenanceStats
} = require('../controllers/maintenance.controller');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all maintenance records
router.get('/', getAllMaintenance);

// Get maintenance statistics
router.get('/stats', getMaintenanceStats);

// Get maintenance by ID
router.get('/:id', getMaintenanceById);

// Create new maintenance record (Manager and above)
router.post('/', requireRole(['Super Admin', 'Admin', 'Manager']), createMaintenance);

// Update maintenance record (Manager and above)
router.put('/:id', requireRole(['Super Admin', 'Admin', 'Manager']), updateMaintenance);

// Delete maintenance record (Admin and above)
router.delete('/:id', requireRole(['Super Admin', 'Admin']), deleteMaintenance);

module.exports = router;