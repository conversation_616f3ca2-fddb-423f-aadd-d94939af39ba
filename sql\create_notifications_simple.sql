-- Simple script to create notifications table
-- Run this in Supabase SQL Editor

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'urgent')),
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT NULL
);

-- Enable Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can manage their own notifications" ON public.notifications
    FOR ALL USING (auth.uid() = user_id);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at DESC);

-- Insert sample data
INSERT INTO public.notifications (user_id, title, message, type, severity, read) 
VALUES 
    (auth.uid(), 'Welcome to Fleet Management', 'Your account has been successfully created!', 'success', 'low', false),
    (auth.uid(), 'System Maintenance', 'Scheduled maintenance will occur tonight from 2-4 AM.', 'warning', 'medium', false),
    (auth.uid(), 'Vehicle Alert', 'Vehicle ABC-123 requires immediate maintenance.', 'error', 'high', false)
ON CONFLICT DO NOTHING;
