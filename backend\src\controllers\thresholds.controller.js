const supabase = require('../utils/supabaseClient');

const getAllThresholds = async (req, res) => {
  try {
    const { check_type, is_active } = req.query;
    
    let query = supabase
      .from('thresholds')
      .select('*');

    // Apply filters
    if (check_type) {
      query = query.eq('check_type', check_type);
    }
    
    if (is_active !== undefined) {
      query = query.eq('is_active', is_active === 'true');
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch thresholds',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get thresholds error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getThresholdById = async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('thresholds')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        message: 'Threshold not found',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get threshold error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const createThreshold = async (req, res) => {
  try {
    const thresholdData = req.body;

    // Validate required fields
    const requiredFields = ['code', 'check_type', 'parameter', 'condition_type', 'value'];
    for (const field of requiredFields) {
      if (!thresholdData[field]) {
        return res.status(400).json({
          success: false,
          message: `${field} is required`
        });
      }
    }

    const { data, error } = await supabase
      .from('thresholds')
      .insert(thresholdData)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create threshold',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'Threshold created successfully',
      data
    });
  } catch (error) {
    console.error('Create threshold error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const updateThreshold = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.id;
    delete updates.created_at;

    const { data, error } = await supabase
      .from('thresholds')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update threshold',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Threshold updated successfully',
      data
    });
  } catch (error) {
    console.error('Update threshold error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const deleteThreshold = async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('thresholds')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to delete threshold',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Threshold deleted successfully'
    });
  } catch (error) {
    console.error('Delete threshold error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getAllThresholds,
  getThresholdById,
  createThreshold,
  updateThreshold,
  deleteThreshold
};