/*
  # إنشاء مخطط قاعدة بيانات إدارة الأسطول

  1. الجداول الجديدة
    - `users` - جدول المستخدمين مع الأدوار والصلاحيات
    - `branches` - جدول الفروع
    - `drivers` - جدول السائقين
    - `vehicles` - جدول المركبات
    - `thresholds` - جدول العتبات والحدود
    - `maintenance` - جدول الصيانة
    - `fuel` - جدول الوقود

  2. الأمان
    - تفعيل RLS على جميع الجداول
    - إضافة سياسات للوصول المحدود حسب الدور
    - ربط المستخدمين بنظام المصادقة في Supabase

  3. الفهارس والقيود
    - إضافة فهارس للبحث السريع
    - قيود المفاتيح الخارجية
    - قيود التحقق من صحة البيانات
*/

-- إ<PERSON><PERSON><PERSON>ء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  auth_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  email text UNIQUE NOT NULL,
  role text NOT NULL CHECK (role IN ('Super Admin', 'Admin', 'Manager', 'Driver')),
  status text NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
  manager_id uuid REFERENCES users(id),
  branch_id uuid,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- إنشاء جدول الفروع
CREATE TABLE IF NOT EXISTS branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  location text NOT NULL,
  address text,
  phone text,
  email text,
  manager_id uuid REFERENCES users(id),
  status text NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- إضافة المرجع للفرع في جدول المستخدمين
ALTER TABLE users ADD CONSTRAINT fk_users_branch 
  FOREIGN KEY (branch_id) REFERENCES branches(id);

-- إنشاء جدول السائقين
CREATE TABLE IF NOT EXISTS drivers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code integer UNIQUE NOT NULL,
  name_en text NOT NULL,
  name_ar text NOT NULL,
  work_number text,
  personal_number text,
  user_profile_url text,
  username text UNIQUE,
  license_number text,
  license_expiry date,
  phone text,
  email text,
  hire_date date DEFAULT CURRENT_DATE,
  branch_id uuid REFERENCES branches(id),
  manager_id uuid REFERENCES users(id),
  user_id uuid REFERENCES users(id),
  status text NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive', 'Suspended')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- إنشاء جدول المركبات
CREATE TABLE IF NOT EXISTS vehicles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id text UNIQUE NOT NULL,
  license_plate text UNIQUE NOT NULL,
  service_type text,
  vehicle_type text,
  make text,
  model text,
  year integer,
  color text,
  vin_number text UNIQUE,
  fuel_type text NOT NULL CHECK (fuel_type IN ('Gasoline', 'Diesel', 'Electric', 'Hybrid', '92', '95')),
  current_km integer DEFAULT 0,
  vehicle_status text NOT NULL DEFAULT 'Active' CHECK (vehicle_status IN ('Active', 'Inactive', 'Maintenance', 'Out of Service')),
  branch_id uuid REFERENCES branches(id),
  current_location text,
  driver_id uuid REFERENCES drivers(id),
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- إنشاء جدول العتبات
CREATE TABLE IF NOT EXISTS thresholds (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code text UNIQUE NOT NULL,
  check_type text NOT NULL CHECK (check_type IN ('Maintenance', 'Tier', 'Fuel', 'Driver')),
  car_type text,
  parameter text NOT NULL,
  condition_type text NOT NULL DEFAULT 'greater_than' CHECK (condition_type IN ('greater_than', 'less_than', 'days_since', 'days_until')),
  value integer NOT NULL,
  unit text DEFAULT 'km',
  description text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- إنشاء جدول الصيانة
CREATE TABLE IF NOT EXISTS maintenance (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id uuid REFERENCES vehicles(id) ON DELETE CASCADE,
  license_plate text,
  service_type text NOT NULL,
  service_center text,
  description text,
  odometer_reading integer,
  parts_cost decimal(10,2) DEFAULT 0,
  labor_cost decimal(10,2) DEFAULT 0,
  total_cost decimal(10,2) DEFAULT 0,
  scheduled_date date,
  service_date date,
  next_service_date date,
  completed_date date,
  status text NOT NULL DEFAULT 'Scheduled' CHECK (status IN ('Scheduled', 'In Progress', 'Completed', 'Cancelled', 'Overdue')),
  mechanic text,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- إنشاء جدول الوقود
CREATE TABLE IF NOT EXISTS fuel (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id uuid REFERENCES vehicles(id) ON DELETE CASCADE,
  license_plate text,
  vehicle_type text,
  vehicle_status text,
  vin text,
  datetime timestamptz DEFAULT now(),
  driver_id uuid REFERENCES drivers(id),
  driver_name_en text,
  driver_name_ar text,
  fuel_type text NOT NULL,
  amount decimal(10,2) NOT NULL,
  quantity decimal(10,2) NOT NULL,
  cost_per_liter decimal(10,2),
  pump_image_reading decimal(10,2),
  difference decimal(10,2),
  odometer integer,
  distance integer,
  consumption_rate decimal(5,2),
  cost_per_meter decimal(10,4),
  branch_id uuid REFERENCES branches(id),
  vehicle_group text,
  vehicle_model integer,
  station text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- إنشاء الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_branch_id ON users(branch_id);
CREATE INDEX IF NOT EXISTS idx_drivers_code ON drivers(code);
CREATE INDEX IF NOT EXISTS idx_drivers_branch_id ON drivers(branch_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_license_plate ON vehicles(license_plate);
CREATE INDEX IF NOT EXISTS idx_vehicles_branch_id ON vehicles(branch_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_driver_id ON vehicles(driver_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_vehicle_id ON maintenance(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_service_date ON maintenance(service_date);
CREATE INDEX IF NOT EXISTS idx_fuel_vehicle_id ON fuel(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_fuel_datetime ON fuel(datetime);

-- تفعيل Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE thresholds ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuel ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للمستخدمين
CREATE POLICY "Users can read own data" ON users
  FOR SELECT TO authenticated
  USING (auth.uid() = auth_id OR role IN ('Super Admin', 'Admin'));

CREATE POLICY "Admins can manage users" ON users
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND u.role IN ('Super Admin', 'Admin')
    )
  );

-- سياسات الأمان للفروع
CREATE POLICY "Users can read branches" ON branches
  FOR SELECT TO authenticated
  USING (true);

CREATE POLICY "Admins can manage branches" ON branches
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND u.role IN ('Super Admin', 'Admin')
    )
  );

-- سياسات الأمان للسائقين
CREATE POLICY "Users can read drivers in their branch" ON drivers
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND (u.role IN ('Super Admin', 'Admin') OR u.branch_id = drivers.branch_id)
    )
  );

CREATE POLICY "Managers can manage drivers" ON drivers
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND u.role IN ('Super Admin', 'Admin', 'Manager')
    )
  );

-- سياسات الأمان للمركبات
CREATE POLICY "Users can read vehicles in their branch" ON vehicles
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND (u.role IN ('Super Admin', 'Admin') OR u.branch_id = vehicles.branch_id)
    )
  );

CREATE POLICY "Managers can manage vehicles" ON vehicles
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND u.role IN ('Super Admin', 'Admin', 'Manager')
    )
  );

-- سياسات الأمان للعتبات
CREATE POLICY "Users can read thresholds" ON thresholds
  FOR SELECT TO authenticated
  USING (true);

CREATE POLICY "Admins can manage thresholds" ON thresholds
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND u.role IN ('Super Admin', 'Admin')
    )
  );

-- سياسات الأمان للصيانة
CREATE POLICY "Users can read maintenance records" ON maintenance
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN vehicles v ON v.branch_id = u.branch_id
      WHERE u.auth_id = auth.uid() 
      AND v.id = maintenance.vehicle_id
      AND (u.role IN ('Super Admin', 'Admin') OR u.branch_id = v.branch_id)
    )
  );

CREATE POLICY "Managers can manage maintenance" ON maintenance
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND u.role IN ('Super Admin', 'Admin', 'Manager')
    )
  );

-- سياسات الأمان للوقود
CREATE POLICY "Users can read fuel records" ON fuel
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN vehicles v ON v.branch_id = u.branch_id
      WHERE u.auth_id = auth.uid() 
      AND v.id = fuel.vehicle_id
      AND (u.role IN ('Super Admin', 'Admin') OR u.branch_id = v.branch_id)
    )
  );

CREATE POLICY "Drivers can add fuel records" ON fuel
  FOR INSERT TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND (u.role IN ('Super Admin', 'Admin', 'Manager', 'Driver'))
    )
  );

CREATE POLICY "Managers can manage fuel records" ON fuel
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.auth_id = auth.uid() 
      AND u.role IN ('Super Admin', 'Admin', 'Manager')
    )
  );