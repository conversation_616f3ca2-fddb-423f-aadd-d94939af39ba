const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth.middleware');
const {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
} = require('../controllers/users.controller');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all users (Admin and Super Admin only)
router.get('/', requireRole(['Super Admin', 'Admin']), getAllUsers);

// Get user by ID
router.get('/:id', getUserById);

// Create new user (Admin and Super Admin only)
router.post('/', requireRole(['Super Admin', 'Admin']), createUser);

// Update user
router.put('/:id', updateUser);

// Delete user (Super Admin only)
router.delete('/:id', requireRole(['Super Admin']), deleteUser);

module.exports = router;