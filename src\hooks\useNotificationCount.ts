import { useState, useEffect, useCallback, useRef } from 'react';
import { supabaseAPI } from '../services/supabase';

interface UseNotificationCountOptions {
  realTime?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const useNotificationCount = (options: UseNotificationCountOptions = {}) => {
  const {
    realTime = true,
    autoRefresh = true,
    refreshInterval = 60000 // 1 minute
  } = options;

  const [data, setData] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const subscriptionRef = useRef<any>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchCount = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await supabaseAPI.notifications.getUnreadCount();
      
      if (result.error) {
        setError(result.error.message);
      } else {
        setData(result.data || 0);
        setLastUpdated(new Date());
      }
    } catch (err: any) {
      setError(err.message);
      console.error('🔔 Fetch notification count error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Real-time subscription setup
  useEffect(() => {
    if (realTime) {
      console.log('🔔 Setting up real-time notification count subscription');
      
      subscriptionRef.current = supabaseAPI.notifications.subscribe((payload) => {
        console.log('🔔 Real-time notification count update:', payload);
        
        switch (payload.eventType) {
          case 'INSERT':
            // Only increment if the new notification is unread
            if (!payload.new.read) {
              setData(prev => prev + 1);
            }
            break;
          case 'UPDATE':
            // Handle read status changes
            if (payload.old.read !== payload.new.read) {
              if (payload.new.read) {
                // Notification was marked as read
                setData(prev => Math.max(0, prev - 1));
              } else {
                // Notification was marked as unread
                setData(prev => prev + 1);
              }
            }
            break;
          case 'DELETE':
            // Only decrement if the deleted notification was unread
            if (!payload.old.read) {
              setData(prev => Math.max(0, prev - 1));
            }
            break;
        }
        setLastUpdated(new Date());
      });

      return () => {
        if (subscriptionRef.current) {
          console.log('🔔 Cleaning up notification count subscription');
          subscriptionRef.current.unsubscribe();
        }
      };
    }
  }, [realTime]);

  // Auto-refresh setup
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      intervalRef.current = setInterval(() => {
        console.log('🔔 Auto-refreshing notification count');
        fetchCount();
      }, refreshInterval);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval, fetchCount]);

  // Initial fetch
  useEffect(() => {
    fetchCount();
  }, [fetchCount]);

  return {
    data,
    loading,
    error,
    lastUpdated,
    refetch: fetchCount
  };
};
