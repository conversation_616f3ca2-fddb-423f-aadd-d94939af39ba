const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth.middleware');
const {
  getDashboardStats,
  getVehicleReport,
  getMaintenanceReport,
  getFuelReport,
  getDriverReport
} = require('../controllers/reports.controller');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get dashboard statistics
router.get('/dashboard', getDashboardStats);

// Get vehicle report (Manager and above)
router.get('/vehicles', requireRole(['Super Admin', 'Admin', 'Manager']), getVehicleReport);

// Get maintenance report (Manager and above)
router.get('/maintenance', requireRole(['Super Admin', 'Admin', 'Manager']), getMaintenanceReport);

// Get fuel report (Manager and above)
router.get('/fuel', requireRole(['Super Admin', 'Admin', 'Manager']), getFuelReport);

// Get driver report (Manager and above)
router.get('/drivers', requireRole(['Super Admin', 'Admin', 'Manager']), getDriverReport);

module.exports = router;