<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .notification {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .success { border-left: 4px solid #10b981; }
        .warning { border-left: 4px solid #f59e0b; }
        .info { border-left: 4px solid #3b82f6; }
        .demo-notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔔 Notifications Test Page</h1>
    
    <div class="demo-notice">
        <strong>Demo Mode Active</strong><br>
        Database connection unavailable. Showing demo notifications for testing purposes.
    </div>

    <div class="notification success">
        <h3>Welcome to Fleet Management</h3>
        <p>Your account has been successfully created! You can now manage your fleet efficiently.</p>
        <small>Just now • success • low</small>
    </div>

    <div class="notification warning">
        <h3>System Maintenance Scheduled</h3>
        <p>Scheduled maintenance will occur tonight from 2-4 AM. Some features may be temporarily unavailable.</p>
        <small>1h ago • warning • medium</small>
    </div>

    <div class="notification info">
        <h3>Vehicle Maintenance Due</h3>
        <p>Vehicle ABC-123 is due for maintenance. Please schedule an appointment.</p>
        <small>2h ago • info • high</small>
    </div>

    <script>
        console.log('✅ Test page loaded successfully');
        console.log('🔔 Demo notifications displayed');
        
        // Test if modern JavaScript features work
        try {
            const testPromise = new Promise((resolve) => {
                setTimeout(() => resolve('Promise works'), 100);
            });
            
            testPromise.then(result => {
                console.log('✅', result);
            });
            
            // Test async/await
            (async () => {
                try {
                    await new Promise(resolve => setTimeout(resolve, 50));
                    console.log('✅ Async/await works');
                } catch (error) {
                    console.error('❌ Async/await error:', error);
                }
            })();
            
        } catch (error) {
            console.error('❌ JavaScript error:', error);
        }
    </script>
</body>
</html>
