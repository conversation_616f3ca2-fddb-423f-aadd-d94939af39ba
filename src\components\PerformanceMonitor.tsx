import React, { useState, useEffect } from 'react';
import { Activity, Database, Clock, Zap, BarChart3 } from 'lucide-react';
import { getCacheStats, clearCache } from '../hooks/useSupabaseData';

interface PerformanceMetrics {
  cacheHits: number;
  cacheMisses: number;
  totalRequests: number;
  averageResponseTime: number;
  lastUpdated: Date;
}

const PerformanceMonitor: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    cacheHits: 0,
    cacheMisses: 0,
    totalRequests: 0,
    averageResponseTime: 0,
    lastUpdated: new Date()
  });

  const [cacheStats, setCacheStats] = useState<any>(null);

  useEffect(() => {
    if (isOpen) {
      const stats = getCacheStats();
      setCacheStats(stats);
    }
  }, [isOpen]);

  const refreshStats = () => {
    const stats = getCacheStats();
    setCacheStats(stats);
    setMetrics(prev => ({
      ...prev,
      lastUpdated: new Date()
    }));
  };

  const handleClearCache = () => {
    if (window.confirm('Are you sure you want to clear all cache? This will force fresh data fetches.')) {
      clearCache();
      refreshStats();
    }
  };

  const formatAge = (ageMs: number) => {
    if (ageMs < 1000) return `${ageMs}ms`;
    if (ageMs < 60000) return `${Math.floor(ageMs / 1000)}s`;
    return `${Math.floor(ageMs / 60000)}m`;
  };

  const getCacheEfficiency = () => {
    if (!cacheStats || cacheStats.cacheSize === 0) return 0;
    const totalRequests = cacheStats.cacheSize + cacheStats.pendingRequests;
    return Math.round((cacheStats.cacheSize / Math.max(totalRequests, 1)) * 100);
  };

  // Only show in development mode
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
        title="Performance Monitor"
      >
        <Activity className="h-5 w-5" />
      </button>

      {/* Performance Panel */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 w-96 bg-white rounded-lg shadow-xl border border-gray-200 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Performance Monitor
            </h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          {/* Cache Statistics */}
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <Database className="h-4 w-4 mr-2" />
                Cache Statistics
              </h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="text-gray-600">Cache Size:</span>
                  <span className="font-medium ml-2">{cacheStats?.cacheSize || 0}</span>
                </div>
                <div>
                  <span className="text-gray-600">Pending:</span>
                  <span className="font-medium ml-2 text-blue-600">{cacheStats?.pendingRequests || 0}</span>
                </div>
                <div>
                  <span className="text-gray-600">Efficiency:</span>
                  <span className="font-medium ml-2 text-green-600">{getCacheEfficiency()}%</span>
                </div>
                <div>
                  <span className="text-gray-600">Stale Entries:</span>
                  <span className="font-medium ml-2 text-yellow-600">
                    {cacheStats?.entries?.filter((e: any) => e.isStale).length || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Cache Entries */}
            {cacheStats && cacheStats.entries.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-3">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  Cache Entries
                </h4>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {cacheStats.entries.map((entry: any, index: number) => (
                    <div key={index} className="flex justify-between items-center text-xs">
                      <span className="text-gray-600 truncate flex-1">
                        {entry.key.slice(0, 20)}...
                      </span>
                      <span className="text-gray-500 ml-2">
                        {formatAge(entry.age)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Performance Metrics */}
            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <Zap className="h-4 w-4 mr-2" />
                Performance Metrics
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Requests:</span>
                  <span className="font-medium">{metrics.totalRequests}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Cache Hits:</span>
                  <span className="font-medium text-green-600">{metrics.cacheHits}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Cache Misses:</span>
                  <span className="font-medium text-red-600">{metrics.cacheMisses}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Avg Response:</span>
                  <span className="font-medium">{metrics.averageResponseTime}ms</span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
              <button
                onClick={refreshStats}
                className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                Refresh Stats
              </button>
              <button
                onClick={handleClearCache}
                className="flex-1 bg-red-600 text-white px-3 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
              >
                Clear Cache
              </button>
            </div>

            {/* Last Updated */}
            <div className="text-xs text-gray-500 text-center">
              Last updated: {metrics.lastUpdated.toLocaleTimeString()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
