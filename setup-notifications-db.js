/**
 * <PERSON><PERSON><PERSON> to set up notifications table in Supabase database
 * Run this to create the notifications table and related functions
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Load environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fjfkoymnkaoxynlumbzt.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY environment variable is required');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupNotificationsTable() {
  try {
    console.log('🚀 Setting up notifications table...');

    // Read the SQL file
    const sqlPath = path.join(process.cwd(), 'database', 'notifications-setup.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });

    if (error) {
      console.error('❌ Error executing SQL:', error);

      // Try alternative approach - execute statements one by one
      console.log('🔄 Trying alternative approach...');
      await executeStatementsIndividually(sql);
    } else {
      console.log('✅ Notifications table setup completed successfully');
      console.log('📊 Result:', data);
    }

    // Test the table
    await testNotificationsTable();

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

async function executeStatementsIndividually(sql) {
  // Split SQL into individual statements
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

  console.log(`📝 Executing ${statements.length} SQL statements...`);

  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    if (statement.trim()) {
      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);

        // For CREATE TABLE, use direct query
        if (statement.includes('CREATE TABLE')) {
          const { error } = await supabase.from('_').select('*').limit(0); // This will fail but establish connection
          // Use raw SQL execution
          const { error: execError } = await supabase.rpc('exec_sql', { sql_query: statement + ';' });
          if (execError && !execError.message.includes('already exists')) {
            console.warn(`⚠️ Warning on statement ${i + 1}:`, execError.message);
          }
        } else {
          // For other statements, try direct execution
          const { error } = await supabase.rpc('exec_sql', { sql_query: statement + ';' });
          if (error && !error.message.includes('already exists')) {
            console.warn(`⚠️ Warning on statement ${i + 1}:`, error.message);
          }
        }
      } catch (error) {
        console.warn(`⚠️ Warning on statement ${i + 1}:`, error.message);
      }
    }
  }
}

async function testNotificationsTable() {
  try {
    console.log('🧪 Testing notifications table...');

    // Test basic table access
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Table test failed:', error);
      return false;
    }

    console.log('✅ Notifications table is accessible');
    console.log(`📊 Found ${data?.length || 0} existing notifications`);

    return true;
  } catch (error) {
    console.error('❌ Table test error:', error);
    return false;
  }
}

// Alternative: Create table using direct Supabase client
async function createTableDirectly() {
  console.log('🔧 Creating notifications table directly...');

  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS public.notifications (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
        severity TEXT NOT NULL DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'urgent')),
        read BOOLEAN NOT NULL DEFAULT false,
        action_url TEXT,
        action_text TEXT,
        expires_at TIMESTAMPTZ,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
    );
  `;

  try {
    // This is a simplified approach that should work
    const { error } = await supabase.rpc('exec_sql', { sql_query: createTableSQL });

    if (error) {
      console.error('❌ Direct table creation failed:', error);
      return false;
    }

    console.log('✅ Table created successfully');

    // Enable RLS
    const rlsSQL = `
      ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

      CREATE POLICY IF NOT EXISTS "Users can view own notifications" ON public.notifications
          FOR SELECT USING (auth.uid() = user_id);

      CREATE POLICY IF NOT EXISTS "Users can insert own notifications" ON public.notifications
          FOR INSERT WITH CHECK (auth.uid() = user_id);

      CREATE POLICY IF NOT EXISTS "Users can update own notifications" ON public.notifications
          FOR UPDATE USING (auth.uid() = user_id);

      CREATE POLICY IF NOT EXISTS "Users can delete own notifications" ON public.notifications
          FOR DELETE USING (auth.uid() = user_id);
    `;

    await supabase.rpc('exec_sql', { sql_query: rlsSQL });
    console.log('✅ RLS policies created');

    return true;
  } catch (error) {
    console.error('❌ Direct creation error:', error);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🔔 Notifications Database Setup');
  console.log('================================');

  try {
    // First try the full setup
    await setupNotificationsTable();
  } catch (error) {
    console.log('🔄 Trying simplified approach...');
    await createTableDirectly();
  }

  // Final test
  const success = await testNotificationsTable();

  if (success) {
    console.log('\n🎉 Notifications system is ready!');
    console.log('You can now use the notifications features in your application.');
  } else {
    console.log('\n❌ Setup incomplete. Please check the Supabase dashboard and create the table manually.');
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}