const supabase = require('../utils/supabaseClient');

const getAllMaintenance = async (req, res) => {
  try {
    const { vehicle_id, status, page = 1, limit = 50 } = req.query;
    
    let query = supabase
      .from('maintenance')
      .select(`
        *,
        vehicle:vehicles(license_plate, make, model, branch:branches(name))
      `);

    // Apply filters
    if (vehicle_id) {
      query = query.eq('vehicle_id', vehicle_id);
    }
    
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch maintenance records',
        error: error.message
      });
    }

    res.json({
      success: true,
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get maintenance error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getMaintenanceById = async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('maintenance')
      .select(`
        *,
        vehicle:vehicles(
          license_plate,
          make,
          model,
          current_km,
          branch:branches(name)
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance record not found',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get maintenance error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const createMaintenance = async (req, res) => {
  try {
    const maintenanceData = req.body;

    // Validate required fields
    const requiredFields = ['vehicle_id', 'service_type'];
    for (const field of requiredFields) {
      if (!maintenanceData[field]) {
        return res.status(400).json({
          success: false,
          message: `${field} is required`
        });
      }
    }

    // Calculate total cost if parts and labor costs are provided
    if (maintenanceData.parts_cost || maintenanceData.labor_cost) {
      maintenanceData.total_cost = 
        (maintenanceData.parts_cost || 0) + (maintenanceData.labor_cost || 0);
    }

    const { data, error } = await supabase
      .from('maintenance')
      .insert(maintenanceData)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create maintenance record',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'Maintenance record created successfully',
      data
    });
  } catch (error) {
    console.error('Create maintenance error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const updateMaintenance = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.id;
    delete updates.created_at;

    // Calculate total cost if parts and labor costs are provided
    if (updates.parts_cost !== undefined || updates.labor_cost !== undefined) {
      const { data: current } = await supabase
        .from('maintenance')
        .select('parts_cost, labor_cost')
        .eq('id', id)
        .single();

      if (current) {
        updates.total_cost = 
          (updates.parts_cost ?? current.parts_cost) + 
          (updates.labor_cost ?? current.labor_cost);
      }
    }

    const { data, error } = await supabase
      .from('maintenance')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update maintenance record',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Maintenance record updated successfully',
      data
    });
  } catch (error) {
    console.error('Update maintenance error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const deleteMaintenance = async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('maintenance')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to delete maintenance record',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Maintenance record deleted successfully'
    });
  } catch (error) {
    console.error('Delete maintenance error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getMaintenanceStats = async (req, res) => {
  try {
    const { data: maintenance, error } = await supabase
      .from('maintenance')
      .select('status, service_type, total_cost, scheduled_date');

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch maintenance stats',
        error: error.message
      });
    }

    const stats = {
      total: maintenance.length,
      byStatus: maintenance.reduce((acc, record) => {
        acc[record.status] = (acc[record.status] || 0) + 1;
        return acc;
      }, {}),
      byServiceType: maintenance.reduce((acc, record) => {
        acc[record.service_type] = (acc[record.service_type] || 0) + 1;
        return acc;
      }, {}),
      totalCost: maintenance.reduce((sum, record) => sum + (record.total_cost || 0), 0),
      averageCost: maintenance.length > 0 
        ? maintenance.reduce((sum, record) => sum + (record.total_cost || 0), 0) / maintenance.length 
        : 0
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get maintenance stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getAllMaintenance,
  getMaintenanceById,
  createMaintenance,
  updateMaintenance,
  deleteMaintenance,
  getMaintenanceStats
};