-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    severity TEXT NOT NULL DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'urgent')),
    read BOOLEAN NOT NULL DEFAULT false,
    action_url TEXT,
    action_text TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_severity ON public.notifications(severity);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON public.notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON public.notifications(expires_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON public.notifications 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own notifications
CREATE POLICY "Users can view own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own notifications
CREATE POLICY "Users can insert own notifications" ON public.notifications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own notifications
CREATE POLICY "Users can update own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own notifications
CREATE POLICY "Users can delete own notifications" ON public.notifications
    FOR DELETE USING (auth.uid() = user_id);

-- Allow service role to manage all notifications (for system notifications)
CREATE POLICY "Service role can manage all notifications" ON public.notifications
    FOR ALL USING (auth.role() = 'service_role');

-- Create function to clean up expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.notifications 
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create system notifications
CREATE OR REPLACE FUNCTION create_system_notification(
    target_user_id UUID,
    notification_title TEXT,
    notification_message TEXT,
    notification_type TEXT DEFAULT 'info',
    notification_severity TEXT DEFAULT 'medium',
    notification_action_url TEXT DEFAULT NULL,
    notification_action_text TEXT DEFAULT NULL,
    notification_expires_at TIMESTAMPTZ DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        user_id,
        title,
        message,
        type,
        severity,
        action_url,
        action_text,
        expires_at
    ) VALUES (
        target_user_id,
        notification_title,
        notification_message,
        notification_type,
        notification_severity,
        notification_action_url,
        notification_action_text,
        notification_expires_at
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to broadcast notifications to all users
CREATE OR REPLACE FUNCTION broadcast_notification(
    notification_title TEXT,
    notification_message TEXT,
    notification_type TEXT DEFAULT 'info',
    notification_severity TEXT DEFAULT 'medium',
    notification_action_url TEXT DEFAULT NULL,
    notification_action_text TEXT DEFAULT NULL,
    notification_expires_at TIMESTAMPTZ DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    user_record RECORD;
    notification_count INTEGER := 0;
BEGIN
    -- Insert notification for each user
    FOR user_record IN 
        SELECT id FROM auth.users WHERE email_confirmed_at IS NOT NULL
    LOOP
        PERFORM create_system_notification(
            user_record.id,
            notification_title,
            notification_message,
            notification_type,
            notification_severity,
            notification_action_url,
            notification_action_text,
            notification_expires_at
        );
        notification_count := notification_count + 1;
    END LOOP;
    
    RETURN notification_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert some sample notifications for testing (only if demo user exists)
DO $$
DECLARE
    demo_user_id UUID;
BEGIN
    -- Check if demo user exists
    SELECT id INTO demo_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>' 
    LIMIT 1;
    
    -- If demo user exists, create sample notifications
    IF demo_user_id IS NOT NULL THEN
        INSERT INTO public.notifications (user_id, title, message, type, severity, action_url, action_text) VALUES
        (demo_user_id, 'Welcome to Fleet Management System', 'Your account has been successfully set up. Explore the dashboard to get started.', 'success', 'medium', '/dashboard', 'View Dashboard'),
        (demo_user_id, 'Vehicle Maintenance Due', 'Vehicle ABC-123 is due for scheduled maintenance. Please schedule an appointment.', 'warning', 'high', '/maintenance', 'Schedule Maintenance'),
        (demo_user_id, 'License Expiry Alert', 'Driver John Doe''s license expires in 30 days. Please renew immediately.', 'warning', 'urgent', '/drivers', 'View Drivers'),
        (demo_user_id, 'System Update', 'The Fleet Management System has been updated with new features and improvements.', 'info', 'low', NULL, NULL),
        (demo_user_id, 'Fuel Report Ready', 'Your monthly fuel consumption report is now available for download.', 'info', 'medium', '/reports', 'View Reports');
        
        RAISE NOTICE 'Sample notifications created for demo user';
    ELSE
        RAISE NOTICE 'Demo user not found, skipping sample notifications';
    END IF;
END $$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.notifications TO authenticated;
GRANT SELECT ON public.notifications TO anon;

-- Enable realtime for notifications table
ALTER PUBLICATION supabase_realtime ADD TABLE public.notifications;
