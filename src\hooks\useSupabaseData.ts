import { useState, useEffect, useRef, useCallback } from 'react';
import { supabaseAPI } from '../services/supabase';

interface UseSupabaseDataOptions {
  enabled?: boolean;
  refetchInterval?: number;
  cacheTime?: number; // Cache duration in milliseconds
  staleTime?: number; // Time before data is considered stale
}

// Simple in-memory cache
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  staleTime: number;
}

const cache = new Map<string, CacheEntry<any>>();

// Request deduplication - prevent multiple identical requests
const pendingRequests = new Map<string, Promise<any>>();

// Cache cleanup function
const cleanupCache = () => {
  const now = Date.now();
  for (const [key, entry] of cache.entries()) {
    if (now - entry.timestamp > entry.staleTime) {
      cache.delete(key);
    }
  }
};

export function useSupabaseData<T>(
  fetchFunction: () => Promise<{ data: T | null; error: any }>,
  options: UseSupabaseDataOptions = {},
  cacheKey?: string
) {
  const {
    enabled = true,
    refetchInterval,
    cacheTime = 5 * 60 * 1000, // 5 minutes default cache
    staleTime = 30 * 1000 // 30 seconds default stale time
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState<number>(0);
  const fetchingRef = useRef(false);
  const mountedRef = useRef(true);

  // Generate cache key if not provided
  const finalCacheKey = cacheKey || fetchFunction.toString();

  const fetchData = useCallback(async (force = false) => {
    if (!enabled) return;

    // Check cache first
    if (!force && finalCacheKey) {
      const cached = cache.get(finalCacheKey);
      if (cached && Date.now() - cached.timestamp < cached.staleTime) {
        console.log(`📦 Cache hit for ${finalCacheKey.slice(0, 50)}...`);
        setData(cached.data);
        setLoading(false);
        setError(null);
        return;
      }
    }

    // Check for pending request (deduplication)
    if (!force && finalCacheKey && pendingRequests.has(finalCacheKey)) {
      console.log(`⏳ Request deduplication for ${finalCacheKey.slice(0, 50)}...`);
      try {
        const result = await pendingRequests.get(finalCacheKey);
        if (mountedRef.current && result && !result.error) {
          setData(result.data);
          setLoading(false);
          setError(null);
        }
      } catch (err) {
        // Handle error from deduplicated request
      }
      return;
    }

    // Prevent multiple simultaneous requests from this hook instance
    if (fetchingRef.current) return;
    fetchingRef.current = true;

    try {
      setLoading(true);
      setError(null);

      // Create and store the request promise for deduplication
      const fetchPromise = fetchFunction();
      if (finalCacheKey) {
        pendingRequests.set(finalCacheKey, fetchPromise);
      }

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 15000)
      );

      const result = await Promise.race([fetchPromise, timeoutPromise]) as any;

      if (!mountedRef.current) return; // Component unmounted

      if (result.error) {
        setError(result.error.message || 'An error occurred');
        setData(null);
      } else {
        setData(result.data);
        setLastFetch(Date.now());

        // Cache the result
        if (finalCacheKey && result.data) {
          cache.set(finalCacheKey, {
            data: result.data,
            timestamp: Date.now(),
            staleTime: cacheTime
          });
          console.log(`💾 Cached data for ${finalCacheKey.slice(0, 50)}...`);
        }
      }
    } catch (err: any) {
      if (!mountedRef.current) return; // Component unmounted

      console.error('Data fetch error:', err);
      setError(err.message || 'An error occurred');
      setData(null);
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
      fetchingRef.current = false;

      // Clean up pending request
      if (finalCacheKey) {
        pendingRequests.delete(finalCacheKey);
      }
    }
  }, [enabled, fetchFunction, finalCacheKey, cacheTime]);

  useEffect(() => {
    mountedRef.current = true;
    fetchData();

    // Cleanup cache periodically
    const cacheCleanup = setInterval(cleanupCache, 60000); // Every minute

    if (refetchInterval && enabled) {
      const interval = setInterval(() => fetchData(), refetchInterval);
      return () => {
        clearInterval(interval);
        clearInterval(cacheCleanup);
        mountedRef.current = false;
      };
    }

    return () => {
      clearInterval(cacheCleanup);
      mountedRef.current = false;
    };
  }, [enabled, refetchInterval, fetchData]);

  const refetch = useCallback(() => {
    fetchData(true); // Force refresh, bypass cache
  }, [fetchData]);

  const isStale = lastFetch > 0 && Date.now() - lastFetch > staleTime;

  return {
    data,
    loading,
    error,
    refetch,
    isStale,
    lastFetch: new Date(lastFetch)
  };
}

// Specific hooks for different data types with optimized caching
export const useVehicles = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.vehicles.getAll(),
    { cacheTime: 10 * 60 * 1000, staleTime: 2 * 60 * 1000, ...options }, // 10min cache, 2min stale
    'vehicles'
  );
};

export const useDrivers = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.drivers.getAll(),
    { cacheTime: 10 * 60 * 1000, staleTime: 2 * 60 * 1000, ...options },
    'drivers'
  );
};

export const useBranches = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.branches.getAll(),
    { cacheTime: 30 * 60 * 1000, staleTime: 10 * 60 * 1000, ...options }, // Branches change less frequently
    'branches'
  );
};

export const useMaintenance = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.maintenance.getAll(),
    { cacheTime: 5 * 60 * 1000, staleTime: 1 * 60 * 1000, ...options }, // More frequent updates
    'maintenance'
  );
};

export const useFuel = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.fuel.getAll(),
    { cacheTime: 5 * 60 * 1000, staleTime: 1 * 60 * 1000, ...options },
    'fuel'
  );
};

export const useThresholds = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.thresholds.getAll(),
    { cacheTime: 30 * 60 * 1000, staleTime: 10 * 60 * 1000, ...options }, // Thresholds rarely change
    'thresholds'
  );
};

export const useDashboardStats = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.dashboard.getStats(),
    { cacheTime: 2 * 60 * 1000, staleTime: 30 * 1000, ...options }, // Stats need frequent updates
    'dashboard-stats'
  );
};

export const useUsers = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.users.getAll(),
    { cacheTime: 15 * 60 * 1000, staleTime: 5 * 60 * 1000, ...options },
    'users'
  );
};

export const useNotifications = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.notifications.getAll(),
    { cacheTime: 1 * 60 * 1000, staleTime: 10 * 1000, ...options }, // Notifications need frequent updates
    'notifications'
  );
};

export const useNotificationCount = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(
    () => supabaseAPI.notifications.getUnreadCount(),
    { cacheTime: 30 * 1000, staleTime: 5 * 1000, ...options }, // Very frequent updates for count
    'notification-count'
  );
};

// Utility function to clear specific cache entries
export const clearCache = (keys?: string[]) => {
  if (keys) {
    keys.forEach(key => {
      cache.delete(key);
      pendingRequests.delete(key);
    });
    console.log(`🗑️ Cleared cache for keys: ${keys.join(', ')}`);
  } else {
    cache.clear();
    pendingRequests.clear();
    console.log('🗑️ Cleared all cache and pending requests');
  }
};

// Utility function to get cache stats
export const getCacheStats = () => {
  const stats = {
    cacheSize: cache.size,
    pendingRequests: pendingRequests.size,
    keys: Array.from(cache.keys()),
    pendingKeys: Array.from(pendingRequests.keys()),
    entries: Array.from(cache.entries()).map(([key, entry]) => ({
      key,
      age: Date.now() - entry.timestamp,
      staleTime: entry.staleTime,
      isStale: Date.now() - entry.timestamp > entry.staleTime
    }))
  };
  console.log('📊 Cache stats:', stats);
  return stats;
};