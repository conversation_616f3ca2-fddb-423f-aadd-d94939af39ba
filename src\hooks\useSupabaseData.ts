import { useState, useEffect } from 'react';
import { supabaseAPI } from '../services/supabase';

interface UseSupabaseDataOptions {
  enabled?: boolean;
  refetchInterval?: number;
}

export function useSupabaseData<T>(
  fetchFunction: () => Promise<{ data: T | null; error: any }>,
  options: UseSupabaseDataOptions = {}
) {
  const { enabled = true, refetchInterval } = options;
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    if (!enabled) return;
    
    try {
      setLoading(true);
      setError(null);
      const result = await fetchFunction();
      
      if (result.error) {
        setError(result.error.message || 'An error occurred');
        setData(null);
      } else {
        setData(result.data);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();

    if (refetchInterval && enabled) {
      const interval = setInterval(fetchData, refetchInterval);
      return () => clearInterval(interval);
    }
  }, [enabled, refetchInterval]);

  const refetch = () => {
    fetchData();
  };

  return { data, loading, error, refetch };
}

// Specific hooks for different data types
export const useVehicles = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(() => supabaseAPI.vehicles.getAll(), options);
};

export const useDrivers = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(() => supabaseAPI.drivers.getAll(), options);
};

export const useBranches = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(() => supabaseAPI.branches.getAll(), options);
};

export const useMaintenance = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(() => supabaseAPI.maintenance.getAll(), options);
};

export const useFuel = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(() => supabaseAPI.fuel.getAll(), options);
};

export const useThresholds = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(() => supabaseAPI.thresholds.getAll(), options);
};

export const useDashboardStats = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(() => supabaseAPI.dashboard.getStats(), options);
};

export const useUsers = (options?: UseSupabaseDataOptions) => {
  return useSupabaseData(() => supabaseAPI.users.getAll(), options);
};