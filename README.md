# Fleet Management System

A comprehensive, professional Fleet Management System built with React, Node.js, and Supabase. This system provides complete vehicle fleet management capabilities including vehicle tracking, driver management, maintenance scheduling, fuel monitoring, and comprehensive reporting.

## 🚀 Features

### Core Functionality
- **Dashboard** - Real-time fleet overview with key metrics and analytics
- **Vehicle Management** - Complete vehicle lifecycle management
- **Driver Management** - Driver profiles, licensing, and performance tracking
- **Branch Management** - Multi-location fleet management
- **Maintenance Scheduling** - Preventive and corrective maintenance tracking
- **Fuel Management** - Consumption monitoring and cost analysis
- **Alert Thresholds** - Configurable automated alerts and notifications
- **Comprehensive Reports** - Advanced analytics and reporting capabilities

### Technical Features
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile devices
- **Modern UI/UX** - Clean, professional interface with intuitive navigation
- **Real-time Updates** - Live data updates and notifications via Supabase
- **Role-based Access** - Secure authentication and authorization (4 user roles)
- **Data Visualization** - Interactive charts and graphs using Recharts
- **RESTful API** - Comprehensive backend API with proper documentation
- **Database Security** - Row Level Security (RLS) with Supabase PostgreSQL

## 🏗️ Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS for responsive design
- **State Management**: React Context API
- **Routing**: React Router v6
- **Charts**: Recharts for data visualization
- **Icons**: Lucide React
- **Build Tool**: Vite

### Backend (Node.js + Express)
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with JWT
- **Security**: Role-based access control, CORS, rate limiting
- **Documentation**: Comprehensive API docs

### Database (Supabase PostgreSQL)
- **Tables**: 7 main entities (users, branches, drivers, vehicles, maintenance, fuel, thresholds)
- **Security**: Row Level Security (RLS) policies
- **Relationships**: Proper foreign key constraints
- **Indexing**: Optimized for performance
- **Triggers**: Automatic timestamp updates

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ and npm 8+
- Supabase account and project
- Git

### 1. Clone Repository
```bash
git clone <repository-url>
cd fleet-management-system
```

### 2. Supabase Setup

1. **Create Supabase Project:**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Note your project URL and anon key

2. **Run Database Migrations:**
   - Go to Supabase Dashboard → SQL Editor
   - Run the migration files in order:
     - `supabase/migrations/20250703031443_crystal_grove.sql`
     - `supabase/migrations/20250703031524_fragrant_paper.sql`
     - `supabase/migrations/20250704190834_bronze_grove.sql`

3. **Configure Authentication:**
   - Go to Authentication → Settings
   - Enable email/password authentication
   - Disable email confirmation for development

### 3. Backend Setup

```bash
cd backend
npm install
cp .env.example .env
```

Edit `backend/.env`:
```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
```

Start backend:
```bash
npm run dev
```

Backend will run on `http://localhost:5000`

### 4. Frontend Setup

```bash
# From project root
npm install
cp .env.example .env
```

Edit `.env`:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_API_URL=http://localhost:5000/api
```

Start frontend:
```bash
npm run dev
```

Frontend will run on `http://localhost:5173`

## 🔐 User Roles & Permissions

### 1. Super Admin
- **Full system access**
- Manage all users, branches, vehicles, drivers
- Delete users and branches
- Access all reports and analytics
- Configure system thresholds

### 2. Admin
- **Administrative access**
- Manage users (except deletion), branches, vehicles, drivers
- Access all reports and analytics
- Configure system thresholds
- Cannot delete users or branches

### 3. Manager
- **Operational management**
- Manage vehicles, drivers, maintenance, fuel records
- Access operational reports
- View branch-specific data
- Cannot manage users or system settings

### 4. Driver
- **Limited access**
- View assigned vehicles
- Add fuel records
- View personal fuel history
- Cannot access management features

## 📊 Database Schema

### Core Tables

1. **users** - System users with roles and authentication
2. **branches** - Fleet locations/offices
3. **drivers** - Driver profiles and information
4. **vehicles** - Vehicle inventory and details
5. **maintenance** - Maintenance records and scheduling
6. **fuel** - Fuel consumption and cost tracking
7. **thresholds** - Alert configuration and rules

### Key Relationships
- Users belong to branches
- Drivers are assigned to branches
- Vehicles are assigned to branches and drivers
- Maintenance records link to vehicles
- Fuel records link to vehicles and drivers

## 🔌 API Documentation

### Base URL
```
http://localhost:5000/api
```

### Authentication
```bash
# Login to get JWT token
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password"
}

# Use token in subsequent requests
Authorization: Bearer <jwt_token>
```

### Main Endpoints

#### Vehicles
- `GET /api/vehicles` - List all vehicles
- `GET /api/vehicles/:id` - Get vehicle details
- `POST /api/vehicles` - Create vehicle (Manager+)
- `PUT /api/vehicles/:id` - Update vehicle (Manager+)
- `DELETE /api/vehicles/:id` - Delete vehicle (Admin+)

#### Drivers
- `GET /api/drivers` - List all drivers
- `GET /api/drivers/:id` - Get driver details
- `POST /api/drivers` - Create driver (Manager+)
- `PUT /api/drivers/:id` - Update driver (Manager+)
- `DELETE /api/drivers/:id` - Delete driver (Admin+)

#### Reports
- `GET /api/reports/dashboard` - Dashboard statistics
- `GET /api/reports/vehicles` - Vehicle reports (Manager+)
- `GET /api/reports/maintenance` - Maintenance reports (Manager+)
- `GET /api/reports/fuel` - Fuel reports (Manager+)

See `backend/README.md` for complete API documentation.

## 🎨 UI/UX Features

### Design System
- **Apple-level aesthetics** with attention to detail
- **Consistent color palette** with proper contrast ratios
- **Typography hierarchy** with proper spacing
- **Micro-interactions** and hover states
- **Loading states** and error handling
- **Responsive breakpoints** for all devices

### User Experience
- **Intuitive navigation** with role-based menus
- **Search and filtering** across all data tables
- **Bulk operations** for efficiency
- **Real-time notifications** for important events
- **Progressive disclosure** to manage complexity
- **Keyboard shortcuts** for power users

## 📈 Reporting & Analytics

### Dashboard Metrics
- Fleet availability percentage
- Active vs inactive vehicles
- Maintenance completion rates
- Fuel consumption trends
- Cost analysis and budgeting
- Driver performance metrics

### Report Types
- **Vehicle Reports** - Utilization, costs, maintenance history
- **Driver Reports** - Performance, fuel efficiency, safety scores
- **Maintenance Reports** - Scheduled vs completed, costs, trends
- **Fuel Reports** - Consumption patterns, cost analysis, efficiency
- **Financial Reports** - Total costs, budget vs actual, ROI

### Export Options
- PDF reports with charts and tables
- Excel exports for further analysis
- CSV data exports
- Scheduled report delivery (future feature)

## 🔒 Security Features

### Authentication & Authorization
- **JWT-based authentication** via Supabase
- **Role-based access control** with 4 user levels
- **Session management** with automatic refresh
- **Password security** with Supabase best practices

### Database Security
- **Row Level Security (RLS)** policies
- **Branch-based data isolation**
- **Audit trails** for all operations
- **Input validation** and sanitization
- **SQL injection prevention**

### API Security
- **CORS configuration** for frontend domain
- **Rate limiting** to prevent abuse
- **Request validation** with proper error handling
- **Secure headers** and middleware

## 🚀 Deployment

### Frontend Deployment (Netlify/Vercel)
```bash
npm run build
# Deploy dist/ folder to your hosting provider
```

### Backend Deployment (Railway/Heroku/DigitalOcean)
```bash
cd backend
npm install --production
npm start
```

### Environment Variables
Ensure all production environment variables are set:
- Supabase URLs and keys
- Frontend/backend URLs
- Security configurations

### Database Migration
- Run all migration files in Supabase Dashboard
- Verify RLS policies are active
- Test with production data

## 🧪 Testing

### Frontend Testing
```bash
npm run test        # Run unit tests
npm run test:e2e    # Run end-to-end tests (when implemented)
npm run lint        # Check code quality
```

### Backend Testing
```bash
cd backend
npm test           # Run API tests (when implemented)
npm run lint       # Check code quality
```

### Manual Testing
- Test all user roles and permissions
- Verify responsive design on different devices
- Test API endpoints with different data
- Validate security policies

## 📚 Documentation

### Available Documentation
- **README.md** - This overview document
- **backend/README.md** - Complete API documentation
- **API Endpoints** - Interactive documentation at `/api`
- **Database Schema** - ERD and table descriptions
- **Deployment Guide** - Step-by-step deployment instructions

### Code Documentation
- **TypeScript interfaces** for type safety
- **JSDoc comments** for complex functions
- **Inline comments** for business logic
- **Component documentation** with props and usage

## 🔄 Development Workflow

### Git Workflow
```bash
git checkout -b feature/new-feature
# Make changes
git commit -m "feat: add new feature"
git push origin feature/new-feature
# Create pull request
```

### Code Standards
- **TypeScript** for type safety
- **ESLint** for code quality
- **Prettier** for code formatting
- **Conventional commits** for clear history

### Development Tools
- **VS Code** with recommended extensions
- **React DevTools** for debugging
- **Supabase Dashboard** for database management
- **Postman** for API testing

## 🛣️ Roadmap

### Phase 1 (Current) ✅
- Core fleet management features
- User authentication and roles
- Basic reporting and analytics
- Responsive web interface

### Phase 2 (Next)
- [ ] Mobile application (React Native)
- [ ] Advanced analytics with ML insights
- [ ] GPS tracking integration
- [ ] Real-time notifications
- [ ] Advanced reporting with custom dashboards

### Phase 3 (Future)
- [ ] IoT sensor integration
- [ ] Route optimization
- [ ] Predictive maintenance
- [ ] Multi-language support
- [ ] API rate limiting and quotas
- [ ] Advanced security features

## 🤝 Contributing

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Code Guidelines
- Follow existing code style
- Add TypeScript types for new features
- Update documentation for API changes
- Test your changes thoroughly

### Issue Reporting
- Use GitHub issues for bug reports
- Provide detailed reproduction steps
- Include environment information
- Suggest solutions if possible

## 📞 Support

### Getting Help
- **Documentation** - Check README files and API docs
- **GitHub Issues** - Report bugs and request features
- **Supabase Dashboard** - Monitor database and auth
- **Browser DevTools** - Debug frontend issues

### Common Issues
- **Authentication errors** - Check Supabase configuration
- **Database errors** - Verify RLS policies and permissions
- **CORS issues** - Check frontend/backend URL configuration
- **Build errors** - Ensure all dependencies are installed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Supabase** for providing excellent backend-as-a-service
- **React Team** for the amazing frontend framework
- **Tailwind CSS** for the utility-first CSS framework
- **Lucide** for the beautiful icon library
- **Recharts** for the powerful charting library

---

**Built with ❤️ for efficient fleet management**

### Quick Start Commands

```bash
# Clone and setup
git clone <repo-url> && cd fleet-management-system

# Setup backend
cd backend && npm install && cp .env.example .env
# Edit .env with Supabase credentials
npm run dev

# Setup frontend (new terminal)
cd .. && npm install && cp .env.example .env
# Edit .env with Supabase credentials
npm run dev

# Access application
# Frontend: http://localhost:5173
# Backend: http://localhost:5000
# Demo login: <EMAIL> / password
```