const supabase = require('../utils/supabaseClient');

const getAllUsers = async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        branch:branches(name),
        manager:users!users_manager_id_fkey(name)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch users',
        error: error.message
      });
    }

    res.json({
      success: true,
      data,
      count: data.length
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        branch:branches(name),
        manager:users!users_manager_id_fkey(name)
      `)
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        error: error.message
      });
    }

    res.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const createUser = async (req, res) => {
  try {
    const { email, password, name, role, branch_id, manager_id } = req.body;

    // Validate required fields
    if (!email || !password || !name || !role) {
      return res.status(400).json({
        success: false,
        message: 'Email, password, name, and role are required'
      });
    }

    // Create auth user first
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: { name, role }
    });

    if (authError) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create auth user',
        error: authError.message
      });
    }

    // Create user profile
    const { data, error } = await supabase
      .from('users')
      .insert({
        auth_id: authData.user.id,
        name,
        email,
        role,
        branch_id,
        manager_id,
        status: 'Active'
      })
      .select()
      .single();

    if (error) {
      // Cleanup auth user if profile creation fails
      await supabase.auth.admin.deleteUser(authData.user.id);
      
      return res.status(400).json({
        success: false,
        message: 'Failed to create user profile',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.id;
    delete updates.auth_id;
    delete updates.created_at;

    const { data, error } = await supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update user',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'User updated successfully',
      data
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // Get user to find auth_id
    const { data: user, error: getUserError } = await supabase
      .from('users')
      .select('auth_id')
      .eq('id', id)
      .single();

    if (getUserError) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Delete user profile (this will cascade to auth user due to foreign key)
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to delete user',
        error: error.message
      });
    }

    // Also delete from auth if auth_id exists
    if (user.auth_id) {
      await supabase.auth.admin.deleteUser(user.auth_id);
    }

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
};