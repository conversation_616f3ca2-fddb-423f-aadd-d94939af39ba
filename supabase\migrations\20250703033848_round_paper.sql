/*
  # Fix infinite recursion in users table RLS policies

  1. Problem
    - The existing RLS policies on the users table are causing infinite recursion
    - The "Users can read own data" policy tries to check the role field from the same table it's protecting
    - This creates a circular dependency during policy evaluation

  2. Solution
    - Drop the problematic policies
    - Create new policies that avoid circular references
    - Use auth.uid() directly without joining back to the users table for basic access
    - Create separate policies for admin access that are more carefully structured

  3. Security
    - Users can read their own data using auth.uid() = auth_id
    - Admins get broader access through a separate policy
    - All policies enable RLS and maintain security
*/

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can read own data" ON users;
DROP POLICY IF EXISTS "Admins can manage users" ON users;

-- Create new policies that avoid recursion

-- Policy 1: Users can read their own profile data
CREATE POLICY "Users can read own profile"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = auth_id);

-- Policy 2: Users can update their own profile data (limited fields)
CREATE POLICY "Users can update own profile"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = auth_id)
  WITH CHECK (auth.uid() = auth_id);

-- Policy 3: Super Admins and Admins can read all users
-- This uses a subquery to avoid direct recursion
CREATE POLICY "Admins can read all users"
  ON users
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.auth_id = auth.uid() 
      AND admin_user.role IN ('Super Admin', 'Admin')
      LIMIT 1
    )
  );

-- Policy 4: Super Admins and Admins can insert new users
CREATE POLICY "Admins can create users"
  ON users
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.auth_id = auth.uid() 
      AND admin_user.role IN ('Super Admin', 'Admin')
      LIMIT 1
    )
  );

-- Policy 5: Super Admins and Admins can update users
CREATE POLICY "Admins can update users"
  ON users
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.auth_id = auth.uid() 
      AND admin_user.role IN ('Super Admin', 'Admin')
      LIMIT 1
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.auth_id = auth.uid() 
      AND admin_user.role IN ('Super Admin', 'Admin')
      LIMIT 1
    )
  );

-- Policy 6: Super Admins can delete users
CREATE POLICY "Super Admins can delete users"
  ON users
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users admin_user 
      WHERE admin_user.auth_id = auth.uid() 
      AND admin_user.role = 'Super Admin'
      LIMIT 1
    )
  );